import { isNil } from 'lodash'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { parsePhoneNumber } from 'react-phone-number-input/input'
import { useDispatch } from 'react-redux'

import { generateUserStatus } from 'api/admin'
import { UserStatusCode, type UserNormalizedStatus } from 'api/admin/types'
import apiCaller, { apiCallerNoX } from 'api/api-caller'
import { useMutationErrorHandlerWithSnackbar } from 'api/helpers'
import type { VehicleId } from 'api/types'
import { parseBEBoolean } from 'api/utils'
import { actions } from 'duxs/admin'
import { enqueueSnackbarWithCloseAction } from 'src/components/Snackbar/Notistack/utils'
import { getUpdateOrCreateSubUserProfileErrorMessage } from 'src/modules/admin/manage-users/EditUser/api/update-user-mutations'
import { fleetUserOrRoleVehiclesQuery } from 'src/modules/admin/shared/data-access/vehicles/queries'
import type { BEBoolean } from 'src/types'
import { ctIntl } from 'src/util-components/ctIntl'
import { createQuery } from 'src/util-functions/react-query-utils'

import { secondsToMs } from 'cartrack-utils'

const { fetchUsers } = actions

type ParamsType = string | null

export declare namespace UserInfo {
  type ApiInput<T extends ParamsType> = {
    client_user_id: T
  }

  type BaseUserInfoApiOutput = {
    clientUsers: Array<{ client_user_id: string; user_name: string }>
    userRoles: Array<{
      user_role_id: string
      company_id: string
      tag: string
      is_admin: string
      is_deleted: string
    }>
  }

  type AddUserInfoApiOutput = {
    ct_fleet_client_user_info: BaseUserInfoApiOutput
  }

  type EditUserInfoApiOutput = {
    ct_fleet_client_user_info: {
      user_id: string
      client_user_id: string
      user_name: string
      cell_number: string
      e_mail: string
      is_locked: BEBoolean
      client_user_role_id: string
      client_user_statuses: string
      shared_reminders_allowed: string
      buy_sms_allowed: string
      logo_image_base64: string | null
      allow_subuser_tosee_mainuser_geofences: string
      allow_subuser_toedit_mainuser_geofences: string
      allow_subuser_toesee_subuser_geofences: string
      allow_subuser_toedit_subuser_geofences: string
      allow_subuser_tosee_mainuser_poi: string
      allow_subuser_toedit_mainuser_poi: string
      allow_subuser_toesee_subuser_poi: string
      allow_subuser_toedit_subuser_poi: string
      department_ids: Array<string> | null
      name: string
      language_id: string
      parent_client_user_id: string
      user_role_id?: Array<string>
    } & BaseUserInfoApiOutput
  }

  type ApiOutput<T extends ParamsType> = T extends null
    ? AddUserInfoApiOutput
    : EditUserInfoApiOutput

  type BaseUserInfoReturn = {
    clientUsers: Array<{ client_user_id: string; user_name: string }>
    userRoles: Array<{
      user_role_id: string
      company_id: string
      tag: string
      is_admin: boolean
      is_deleted: boolean
    }>
  }

  type EditUserInfoReturn = {
    user_id: string
    client_user_id: string
    user_name: string
    cell_number: {
      number: string | null
      countryCode: string | null
    }
    e_mail: string
    client_user_role_id: string
    client_user_statuses: string
    status: UserNormalizedStatus
    shared_reminders_allowed: boolean
    buy_sms_allowed: boolean
    logo_image_base64: string | null
    allow_subuser_tosee_mainuser_geofences: boolean
    allow_subuser_toedit_mainuser_geofences: boolean
    allow_subuser_toesee_subuser_geofences: boolean
    allow_subuser_toedit_subuser_geofences: boolean
    allow_subuser_tosee_mainuser_poi: boolean
    allow_subuser_toedit_mainuser_poi: boolean
    allow_subuser_toesee_subuser_poi: boolean
    allow_subuser_toedit_subuser_poi: boolean
    department_ids: Array<string> | null
    name: string
    language_id: string
    parent_client_user_id: string | null
    user_role_id?: Array<string>
  } & BaseUserInfoReturn

  type Return<T extends ParamsType> = T extends null
    ? BaseUserInfoReturn
    : EditUserInfoReturn
}

export const useFetchUserInfoQuery = <T extends ParamsType>(
  params: UserInfo.ApiInput<T>,
) => useQuery(fetchUserInfoQuery(params))

const fetchUserInfoKey = <T extends ParamsType>({
  client_user_id,
}: UserInfo.ApiInput<T>) => ['manageUsers/fetchUserInfo', { client_user_id }] as const

export const fetchUserInfoQuery = <T extends ParamsType>(
  params: UserInfo.ApiInput<T>,
) =>
  createQuery({
    queryKey: fetchUserInfoKey(params),
    queryFn: async () => fetchUserInfo(params),
    staleTime: secondsToMs(30),
  })

async function fetchUserInfo<T extends ParamsType>(
  params: UserInfo.ApiInput<T>,
): Promise<UserInfo.Return<T>> {
  return apiCallerNoX<UserInfo.ApiOutput<T>>('ct_fleet_client_user_info', params).then(
    (data) => parseUserInfo<T>(data),
  )
}

const parseUserInfo = <T extends ParamsType>(
  data: UserInfo.ApiOutput<T>,
): UserInfo.Return<T> => {
  const { ct_fleet_client_user_info: userData } = data
  if ('cell_number' in userData) {
    const parsedPhoneNumber = parsePhoneNumber(userData.cell_number)
    return {
      ...userData,
      status: generateUserStatus({
        clientUserStatus: userData.client_user_statuses,
        isLocked: userData.is_locked,
      }),
      cell_number: {
        number: parsedPhoneNumber?.nationalNumber ?? null,
        countryCode: parsedPhoneNumber?.country ?? null,
      },
      shared_reminders_allowed: parseBEBoolean(userData.shared_reminders_allowed, {
        fallback: false,
      }),
      buy_sms_allowed: parseBEBoolean(userData.buy_sms_allowed, {
        fallback: false,
      }),
      allow_subuser_tosee_mainuser_geofences: parseBEBoolean(
        userData.allow_subuser_tosee_mainuser_geofences,
        { fallback: false },
      ),
      allow_subuser_toedit_mainuser_geofences: parseBEBoolean(
        userData.allow_subuser_toedit_mainuser_geofences,
        { fallback: false },
      ),
      allow_subuser_toesee_subuser_geofences: parseBEBoolean(
        userData.allow_subuser_toesee_subuser_geofences,
        { fallback: false },
      ),
      allow_subuser_toedit_subuser_geofences: parseBEBoolean(
        userData.allow_subuser_toedit_subuser_geofences,
        { fallback: false },
      ),
      allow_subuser_tosee_mainuser_poi: parseBEBoolean(
        userData.allow_subuser_tosee_mainuser_poi,
        { fallback: false },
      ),
      allow_subuser_toedit_mainuser_poi: parseBEBoolean(
        userData.allow_subuser_toedit_mainuser_poi,
        { fallback: false },
      ),
      allow_subuser_toesee_subuser_poi: parseBEBoolean(
        userData.allow_subuser_toesee_subuser_poi,
        { fallback: false },
      ),
      allow_subuser_toedit_subuser_poi: parseBEBoolean(
        userData.allow_subuser_toedit_subuser_poi,
        { fallback: false },
      ),
      userRoles:
        userData?.userRoles?.map((userRole) => ({
          ...userRole,
          is_admin: parseBEBoolean(userRole.is_admin, {
            fallback: false,
          }),
          is_deleted: parseBEBoolean(userRole.is_deleted, {
            fallback: false,
          }),
        })) || [],
    } satisfies UserInfo.EditUserInfoReturn as UserInfo.Return<T>
  } else {
    return {
      clientUsers: userData.clientUsers,
      userRoles:
        userData?.userRoles?.map((userRole) => ({
          ...userRole,
          is_admin: parseBEBoolean(userRole.is_admin, {
            fallback: false,
          }),
          is_deleted: parseBEBoolean(userRole.is_deleted, {
            fallback: false,
          }),
        })) || [],
    } satisfies UserInfo.BaseUserInfoReturn as UserInfo.Return<T>
  }
}
export declare namespace CreateUser {
  type ApiInputFn = {
    user_name: string
    cell_no: string | null
    email_address: string
    department_ids: Array<string> | null
    client_user_role_id: string | null
    parent_id?: string | null
    language_id?: string | null
    name?: string
    user_role_id?: Array<string>
  }

  type ApiInput = {
    data: Array<ApiInputFn>
  }

  type ApiOutput = {
    client_user_id: string
    success: boolean
  }
}

export const useCreateUserMutation = () =>
  useMutation({
    mutationFn: (params: CreateUser.ApiInputFn) => createUser(params),
    onSuccess() {
      enqueueSnackbarWithCloseAction(
        ctIntl.formatMessage({
          id: 'User created!',
        }),
        {
          variant: 'success',
        },
      )
    },
    ...useMutationErrorHandlerWithSnackbar(),
  })

async function createUser(
  params: CreateUser.ApiInputFn,
): Promise<CreateUser.ApiOutput> {
  const apiInput: CreateUser.ApiInput = {
    data: [params],
  }
  const res = await apiCaller('ct_fleet_create_user', apiInput)

  if (!res.success) {
    return Promise.reject(
      new Error(
        typeof res === 'string'
          ? res
          : getUpdateOrCreateSubUserProfileErrorMessage(res),
      ),
    )
  }
  return res
}

export declare namespace UpdateUser {
  type UpdateProfileApiInput = {
    base_features: null
    client_user_id: string
    general: {
      cell_no: string | null
      client_user_role_id: string | null
      department_ids: Array<string> | null
      email_address: string
      user_name: string
    }
    profile: {
      language_id: string | null
      name: string
      parent_id: string | null
      user_role_id: Array<string>
    }
    vehicles: null
  }

  type UpdateDataAccessApiInput = {
    base_features: null
    client_user_id: string
    general: null
    profile: null
    vehicles: {
      all: boolean
      group: Array<VehicleId>
    }
  }

  type ApiInput = UpdateProfileApiInput | UpdateDataAccessApiInput

  type ApiOutput = {
    success: boolean
  }
}

export const useUpdateUserMutation = () => {
  const queryClient = useQueryClient()

  return useMutation({
    onMutate: async ({ client_user_id, vehicles }) => {
      const query = fleetUserOrRoleVehiclesQuery({ data: client_user_id })
      await queryClient.cancelQueries(query)

      const previousQueryData = query.getData(queryClient)
      query.setData(queryClient, {
        updater: (oldData) => {
          if (oldData === undefined) {
            return oldData
          }

          return vehicles?.group ?? []
        },
      })

      // Return a context with the previous data
      return {
        previousUserVehicle: previousQueryData,
        userVehicleKey: query.queryKey,
      }
    },
    mutationFn: (params: UpdateUser.ApiInput) => updateUser(params),
    onSuccess(_, variables) {
      queryClient.invalidateQueries(
        isNil(variables.profile)
          ? fleetUserOrRoleVehiclesQuery({ data: variables.client_user_id }) // when update user profile
          : fetchUserInfoQuery({ client_user_id: variables.client_user_id }), // when update user vehicle data access
      )
    },
    onError: (_error, _vars, context) => {
      if (context !== undefined) {
        queryClient.setQueryData(context?.userVehicleKey, context?.previousUserVehicle)
      }

      enqueueSnackbarWithCloseAction(
        ctIntl.formatMessage({
          id: 'There was an error updating the user vehicle access.',
        }),
        { variant: 'error' },
      )
    },
  })
}

async function updateUser(params: UpdateUser.ApiInput): Promise<UpdateUser.ApiOutput> {
  return apiCaller('ct_fleet_update_user', params)
}

export declare namespace ResetPassword {
  type ApiInput = {
    client_user_id: string
  }

  type ApiOutput = {
    success: boolean
  }
}

export const useResetPasswordMutation = () =>
  useMutation({
    mutationFn: (params: ResetPassword.ApiInput) => resetPassword(params),
    onSuccess() {
      enqueueSnackbarWithCloseAction(
        ctIntl.formatMessage({
          id: 'Password reseted!',
        }),
        { variant: 'success' },
      )
    },
  })

async function resetPassword(
  params: ResetPassword.ApiInput,
): Promise<ResetPassword.ApiOutput> {
  return apiCaller('ct_fleet_user_reset_password', params)
}

export declare namespace ToggleLockUser {
  type ApiInput = {
    client_user_id: string
    is_locked: boolean
  }

  type ApiOutput = {
    success: boolean
  }
}

export const useToggleLockUserMutation = (userId: string) => {
  const queryClient = useQueryClient()
  const dispatch = useDispatch()
  return useMutation({
    mutationFn: (params: ToggleLockUser.ApiInput) => toggleLockUser(params),
    onSuccess(_, variables) {
      enqueueSnackbarWithCloseAction(
        ctIntl.formatMessage({
          id: variables.is_locked ? 'User blocked!' : 'User unblocked!',
        }),
        { variant: 'success' },
      )
      queryClient.invalidateQueries(fetchUserInfoQuery({ client_user_id: userId }))
      dispatch(fetchUsers())
    },
    ...useMutationErrorHandlerWithSnackbar(),
  })
}

async function toggleLockUser(
  params: ToggleLockUser.ApiInput,
): Promise<ToggleLockUser.ApiOutput> {
  return apiCallerNoX<ToggleLockUser.ApiOutput>('ct_fleet_client_user_lock', {
    data: params,
  })
}

export declare namespace ToggleActiveUser {
  type ApiFnInput = {
    client_user_id: string
    isActive: boolean
  }

  type ApiInput = Array<{
    client_user_id: string
    client_user_status: UserStatusCode
    user_stauts_note: '.'
  }>
}

export const useToggleActiveUserMutation = () => {
  const queryClient = useQueryClient()
  const dispatch = useDispatch()
  return useMutation({
    mutationFn: (params: ToggleActiveUser.ApiFnInput) => toggleActiveUser(params),
    onSuccess(_, { client_user_id, isActive }) {
      queryClient.invalidateQueries(
        fetchUserInfoQuery({ client_user_id: client_user_id }),
      )
      dispatch(fetchUsers())
      enqueueSnackbarWithCloseAction(
        ctIntl.formatMessage({
          id: !isActive
            ? 'Successfully deactivated this user'
            : 'Successfully reactivated this user',
        }),
        { variant: 'success' },
      )
    },
    ...useMutationErrorHandlerWithSnackbar(),
  })
}

async function toggleActiveUser(params: ToggleActiveUser.ApiFnInput) {
  // When deactivating a user, we need to pass the active status to the API.
  // Similarly, when activating a user, we need to pass the deactive status to the API.
  const activeStatus = !params.isActive
    ? UserStatusCode.Active
    : UserStatusCode.Deactivated
  return apiCaller('ct_fleet_disable_enable_user', {
    data: [
      {
        user_stauts_note: '.',
        client_user_status: activeStatus,
        client_user_id: params.client_user_id,
      },
    ] satisfies ToggleActiveUser.ApiInput,
  })
}
