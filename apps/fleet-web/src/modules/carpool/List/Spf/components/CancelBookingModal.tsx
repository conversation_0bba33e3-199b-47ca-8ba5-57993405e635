import { useContext, useMemo } from 'react'
import { isEmpty } from 'lodash'
import { zodResolver } from '@hookform/resolvers/zod'
import { Autocomplete, Button, Stack, TextField, Typography } from '@karoo-ui/core'
import { TextFieldControlled } from '@karoo-ui/core-rhf'
import { Controller, useForm } from 'react-hook-form'
import { z } from 'zod'

import { getAutocompleteVirtualizedProps } from 'src/components/_dropdowns/AutocompleteVirtualized'
import ConfirmationModal from 'src/components/_modals/Confirmation'

import { ctIntl } from 'cartrack-ui-kit'
import { showRequiredMessageOnAllErrorTypes } from '../../../utils/helpers'
import { CarpoolOptionsContext } from '../../constants'
import useCancelBookingMutation from '../api/useCancelBookingMutation'

const schema = z.object({
  reason: z.number({
    errorMap: showRequiredMessageOnAllErrorTypes,
  }),
  notes: z.string().nullable(),
})

type Schema = z.infer<typeof schema>

type FormPossibleValues = {
  reason: Schema['reason'] | null
  notes: Schema['notes']
}

type Props = {
  onClose: () => void
  onConfirm?: (reason: string) => void
  bookingIds: Array<string | number>
}

type CancelReasonAutocompleteOption = { id: number; label: string }

function CancelBookingModal({ onClose, bookingIds }: Props) {
  const { carpoolOptionsData } = useContext(CarpoolOptionsContext)
  const cancelBookingMutation = useCancelBookingMutation()

  const initialValues: FormPossibleValues = {
    reason: null,
    notes: '',
  }

  const {
    control,
    handleSubmit,
    setValue: setFormValue,
  } = useForm<FormPossibleValues>({
    resolver: zodResolver(schema),
    mode: 'all',
    defaultValues: initialValues,
  })

  const cancelReasonOptions = useMemo(() => {
    const array: Array<CancelReasonAutocompleteOption> = []
    const byId = new Map<number, CancelReasonAutocompleteOption>()

    if (!isEmpty(carpoolOptionsData)) {
      for (const reason of carpoolOptionsData.cancelReasons) {
        const option = { id: reason.id, label: reason.reason }
        array.push(option)
        byId.set(option.id, option)
      }
    }
    return {
      array,
      byId,
    }
  }, [carpoolOptionsData])

  const submitForm = handleSubmit((_values) => {
    const values = _values as Schema

    const { reason, notes } = values

    cancelBookingMutation.mutate(
      {
        bookingIds: bookingIds as Array<string>,
        bookingCancelReasonId: reason,
        bookingCancelNotes: notes as string,
      },
      {
        onSuccess() {
          onClose()
        },
      },
    )
  })

  return (
    <ConfirmationModal
      title={ctIntl.formatMessage({ id: 'tfms.list.cancel.title' })}
      open
      hasFooterButtons={false}
      isLoading={cancelBookingMutation.isPending}
      onClose={onClose}
    >
      <form onSubmit={submitForm}>
        <Stack
          gap={2}
          mb={2}
        >
          <Typography mb={2}>
            {ctIntl.formatMessage(
              {
                id: 'tfms.cancel.selectReason.description',
              },
              {
                values: { count: bookingIds.length, bookingIds: bookingIds.join(', ') },
              },
            )}
          </Typography>
          <Controller
            control={control}
            name="reason"
            render={({ field, fieldState }) => (
              <Autocomplete
                size="small"
                {...getAutocompleteVirtualizedProps({
                  options: cancelReasonOptions.array,
                })}
                onChange={(_, newValue) => {
                  setFormValue(field.name, newValue ? newValue.id : null, {
                    shouldValidate: true,
                  })
                }}
                value={
                  field.value ? cancelReasonOptions.byId.get(field.value) ?? null : null
                }
                renderInput={(params) => (
                  <TextField
                    {...params}
                    required
                    label={ctIntl.formatMessage({
                      id: 'tfms.cancel.selectReason.label',
                    })}
                    helperText={ctIntl.formatMessage({
                      id: fieldState.error?.message ?? '',
                    })}
                    error={!!fieldState.error}
                  />
                )}
              />
            )}
          />

          <TextFieldControlled
            size="small"
            ControllerProps={{ name: 'notes', control }}
            label={`${ctIntl.formatMessage({ id: 'Notes' })} (${ctIntl.formatMessage({
              id: 'Optional',
            })})`}
            fullWidth
            variant="standard"
          />
        </Stack>

        <Stack
          direction="row"
          justifyContent="flex-end"
          spacing={0.5}
        >
          <Button
            onClick={onClose}
            color="secondary"
            variant="text"
          >
            {ctIntl.formatMessage({ id: 'Close' })}
          </Button>
          <Button
            color="primary"
            variant="text"
            type="submit"
          >
            {ctIntl.formatMessage({ id: 'Confirm' })}
          </Button>
        </Stack>
      </form>
    </ConfirmationModal>
  )
}

export default CancelBookingModal
