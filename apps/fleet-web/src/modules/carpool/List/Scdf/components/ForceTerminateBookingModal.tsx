import useForceTerminateBookingMutation from '../api/useForceTerminateBookingMutation'
import { useForceTerminateReasonsQuery } from '../api/useForceTerminateReasonsQuery'
import BookingActionModalWithReason from './BookingActionModalWithReason'

type ForceTerminateBookingModalProps = {
  onClose: () => void
  onConfirm?: () => void
  bookingIds: Array<string>
}

const ForceTerminateBookingModal = ({
  onClose,
  onConfirm,
  bookingIds,
}: ForceTerminateBookingModalProps) => {
  const reasonsQuery = useForceTerminateReasonsQuery()
  const forceTerminateBookingMutation = useForceTerminateBookingMutation()

  return (
    <BookingActionModalWithReason
      onClose={onClose}
      onConfirm={onConfirm}
      bookingIds={bookingIds}
      titleId="tfms.list.forceTerminate"
      descriptionId="tfms.forceTerminate.selectReason.description"
      reasonLabelId="tfms.forceTerminate.selectReason.label"
      reasonsQuery={reasonsQuery}
      mutation={forceTerminateBookingMutation}
    />
  )
}

export default ForceTerminateBookingModal
