import { useMemo } from 'react'
import { zodResolver } from '@hookform/resolvers/zod'
import { Autocomplete, Stack, TextField } from '@karoo-ui/core'
import { Controller, useForm } from 'react-hook-form'
import { z } from 'zod'

import { getAuthenticatedUser } from 'duxs/user'
import { getAutocompleteVirtualizedProps } from 'src/components/_dropdowns/AutocompleteVirtualized'
import ConfirmationModal from 'src/components/_modals/Confirmation'
import { useTypedSelector } from 'src/redux-hooks'
import { messages } from 'src/shared/formik'
import IntlTypography from 'src/util-components/IntlTypography'

import { ctIntl } from 'cartrack-ui-kit'

const bookingActionSchema = z.object({
  reasonId: z
    .number()
    .nullable()
    .refine((val) => val !== null && val > 0, {
      message: messages.required,
    }),
  remarks: z.string().default(''),
})

type BookingActionFormData = z.infer<typeof bookingActionSchema>

type ReasonOption = {
  id: number
  label: string
}

type BookingActionModalWithReasonProps = {
  onClose: () => void
  onConfirm?: () => void
  bookingIds: Array<string>
  titleId: string
  descriptionId: string
  reasonLabelId: string
  reasonsQuery: {
    data?: Array<{ id: number; title: string }>
    isPending: boolean
  }
  mutation: {
    mutate: (
      params: {
        bookingIds: Array<string>
        reasonId: number
        remarks: string
        clientUserId: string
      },
      options?: { onSuccess?: () => void },
    ) => void
    isPending: boolean
  }
}

const BookingActionModalWithReason = ({
  onClose,
  onConfirm,
  bookingIds,
  titleId,
  descriptionId,
  reasonLabelId,
  reasonsQuery,
  mutation,
}: BookingActionModalWithReasonProps) => {
  const { cuid: clientUserId } = useTypedSelector(getAuthenticatedUser)

  const {
    control,
    handleSubmit,
    reset,
    formState: { isValid },
  } = useForm<BookingActionFormData>({
    resolver: zodResolver(bookingActionSchema),
    mode: 'onChange',
    defaultValues: { reasonId: null as any, remarks: '' },
  })

  const reasonOptions = useMemo(() => {
    const array: Array<ReasonOption> = []
    const byId = new Map<number, ReasonOption>()

    if (reasonsQuery.data) {
      for (const reason of reasonsQuery.data) {
        const option = { id: reason.id, label: reason.title }
        array.push(option)
        byId.set(option.id, option)
      }
    }

    return { array, byId }
  }, [reasonsQuery.data])

  const handleModalClose = () => {
    reset()
    onClose()
  }

  const handleFormSubmit = handleSubmit((data) => {
    if (data.reasonId === null) {
      return // This should not happen due to form validation
    }

    mutation.mutate(
      {
        bookingIds,
        reasonId: data.reasonId,
        remarks: data.remarks,
        clientUserId: clientUserId ?? '',
      },
      {
        onSuccess() {
          onClose()
          onConfirm?.()
        },
      },
    )
  })

  return (
    <ConfirmationModal
      open
      onClose={handleModalClose}
      onConfirm={handleFormSubmit}
      title={ctIntl.formatMessage({ id: titleId })}
      confirmButtonLabel="CONFIRM"
      confirmButtonVariant="contained"
      isLoading={mutation.isPending}
      disabledConfirmButton={!isValid}
    >
      <Stack gap={2}>
        <IntlTypography
          color="text.secondary"
          msgProps={{
            id: descriptionId,
            values: { count: bookingIds.length, bookingIds: bookingIds.join(', ') },
          }}
        />

        <Controller
          control={control}
          name="reasonId"
          render={({ field, fieldState }) => (
            <Autocomplete<ReasonOption>
              size="small"
              loading={reasonsQuery.isPending}
              {...getAutocompleteVirtualizedProps({
                options: reasonOptions.array,
              })}
              onChange={(_, newValue) => {
                field.onChange(newValue ? newValue.id : null)
              }}
              value={field.value ? reasonOptions.byId.get(field.value) ?? null : null}
              renderInput={(params) => (
                <TextField
                  {...params}
                  required
                  label={ctIntl.formatMessage({
                    id: reasonLabelId,
                  })}
                  helperText={ctIntl.formatMessage({
                    id: fieldState.error?.message ?? '',
                  })}
                  error={!!fieldState.error}
                />
              )}
            />
          )}
        />

        <Controller
          control={control}
          name="remarks"
          render={({ field }) => (
            <TextField
              {...field}
              label={ctIntl.formatMessage({ id: 'Remarks' })}
              multiline
              rows={3}
              variant="outlined"
              size="small"
              fullWidth
            />
          )}
        />
      </Stack>
    </ConfirmationModal>
  )
}

export default BookingActionModalWithReason
