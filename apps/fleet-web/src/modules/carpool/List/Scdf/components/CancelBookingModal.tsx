import useCancelBookingMutation from '../api/useCancelBookingMutation'
import { useCancelReasonsQuery } from '../api/useCancelReasonsQuery'
import BookingActionModalWithReason from './BookingActionModalWithReason'

type CancelBookingModalProps = {
  onClose: () => void
  onConfirm?: () => void
  bookingIds: Array<string>
}

const CancelBookingModal = ({
  onClose,
  onConfirm,
  bookingIds,
}: CancelBookingModalProps) => {
  const reasonsQuery = useCancelReasonsQuery()
  const cancelBookingMutation = useCancelBookingMutation()

  return (
    <BookingActionModalWithReason
      onClose={onClose}
      onConfirm={onConfirm}
      bookingIds={bookingIds}
      titleId="tfms.list.cancel.title"
      descriptionId="tfms.cancel.selectReason.description"
      reasonLabelId="tfms.cancel.selectReason.label"
      reasonsQuery={reasonsQuery}
      mutation={cancelBookingMutation}
    />
  )
}

export default CancelBookingModal
