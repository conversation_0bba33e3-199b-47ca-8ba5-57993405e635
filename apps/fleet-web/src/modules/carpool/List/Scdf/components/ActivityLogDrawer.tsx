import {
  Box,
  CircularProgressDelayedAbsolute,
  IconButton,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Stack,
  Typography,
} from '@karoo-ui/core'
import AccessTimeIcon from '@mui/icons-material/AccessTime'
import CheckCircleIcon from '@mui/icons-material/CheckCircle'
import CloseIcon from '@mui/icons-material/Close'
import DateRangeOutlinedIcon from '@mui/icons-material/DateRangeOutlined'
import DoDisturbAltIcon from '@mui/icons-material/DoDisturbAlt'
import DoneAllIcon from '@mui/icons-material/DoneAll'
import EditIcon from '@mui/icons-material/Edit'
import IndeterminateCheckBoxOutlinedIcon from '@mui/icons-material/IndeterminateCheckBoxOutlined'
import { match } from 'ts-pattern'

import DrawerBase from 'src/modules/components/unconnected/DrawerBase'

import CarpoolStatusChip from '../../../components/CarpoolStatusChip'
import { BookingStatus } from '../../../utils/constants'
import {
  useActivityLogQuery,
  type ActivityLogResponse,
} from '../api/useActivityLogQuery'

type Props = {
  onClose: () => void
  bookingId: number
  bookingStatus: BookingStatus
}

// Combined function to get both icon and text for activity
const getActivityIconAndText = (
  activity: ActivityLogResponse['activities'][number],
) => {
  const { action, user, timestamp } = activity
  const formatedTime = timestamp.toFormat('ff')

  return match(action)
    .with({ statusId: BookingStatus.BOOKING_STATUS_REQUESTED }, () => ({
      icon: <DateRangeOutlinedIcon />,
      primaryText: `${user.id} ${user.name} created this booking request ${formatedTime}`,
      secondaryText: null,
    }))
    .with(
      { statusId: BookingStatus.BOOKING_STATUS_APPROVED },
      ({ formattedAssignmentDetails }) => ({
        icon: <CheckCircleIcon />,
        primaryText: `${user.id} ${user.name} approved this booking request ${formatedTime}`,
        secondaryText: formattedAssignmentDetails || null,
      }),
    )
    .with(
      { statusId: BookingStatus.BOOKING_STATUS_DECLINED },
      ({ reason, remarks }) => {
        const reasonText = `Reason: ${reason}`
        const remarksText = remarks ? `Remarks: ${remarks}` : null
        const secondaryText = [reasonText, remarksText].filter(Boolean).join('\n')

        return {
          icon: <DoDisturbAltIcon />,
          primaryText: `${user.id} ${user.name} rejected this booking request ${formatedTime}`,
          secondaryText,
        }
      },
    )
    .with(
      { statusId: BookingStatus.BOOKING_STATUS_CANCELLED },
      ({ reason, remarks }) => {
        const reasonText = `Reason: ${reason}`
        const remarksText = remarks ? `Remarks: ${remarks}` : null
        const secondaryText = [reasonText, remarksText].filter(Boolean).join('\n')

        return {
          icon: <CloseIcon />,
          primaryText: `${user.id} ${user.name} cancelled this booking request ${formatedTime}`,
          secondaryText,
        }
      },
    )
    .with(
      { statusId: BookingStatus.BOOKING_STATUS_ACTIVE },
      ({ formattedPickupTime }) => ({
        icon: <AccessTimeIcon />,
        primaryText: `${user.id} ${user.name} activated this booking ${formatedTime}`,
        secondaryText: formattedPickupTime
          ? `Actual Pick-up date/time: ${formattedPickupTime}`
          : null,
      }),
    )
    .with(
      { statusId: BookingStatus.BOOKING_STATUS_RETURNED },
      ({ formattedDropoffTime }) => ({
        icon: <DoneAllIcon />,
        primaryText: `${user.id} ${user.name} ended this booking ${formatedTime}`,
        secondaryText: formattedDropoffTime
          ? `Actual Drop-off date/time: ${formattedDropoffTime}`
          : null,
      }),
    )
    .with(
      { statusId: BookingStatus.BOOKING_STATUS_FORCE_TERMINATED },
      ({ reason, remarks }) => {
        const reasonText = `Reason: ${reason}`
        const remarksText = remarks ? `Remarks: ${remarks}` : null
        const secondaryText = [reasonText, remarksText].filter(Boolean).join('\n')

        return {
          icon: <IndeterminateCheckBoxOutlinedIcon />,
          primaryText: `${user.id} ${user.name} forced terminate this booking ${formatedTime}`,
          secondaryText,
        }
      },
    )
    .with({ statusId: 'EDIT' }, ({ formattedChanges }) => {
      const changesText = formattedChanges
        ?.map((change) => change.formattedText)
        .join('\n')

      return {
        icon: <EditIcon />,
        primaryText: `${user.id} ${user.name} edited this booking ${formatedTime}`,
        secondaryText: changesText || null,
      }
    })
    .otherwise(() => ({
      icon: <AccessTimeIcon />,
      primaryText: `${user.id} ${user.name} performed an action`,
      secondaryText: null,
    }))
}

const ActivityLogItem = ({
  activity,
  isLast,
}: {
  activity: ActivityLogResponse['activities'][number]
  isLast: boolean
}) => {
  const { icon, primaryText, secondaryText } = getActivityIconAndText(activity)

  return (
    <ListItem sx={{ px: 0, py: 0, my: 1, gap: 1, alignItems: 'stretch' }}>
      <ListItemIcon
        sx={{
          minWidth: 40,
          minHeight: '100%',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
        }}
      >
        <IconButton
          size="small"
          sx={{
            backgroundColor: 'grey.200',
            flexShrink: 0,
            '& .MuiSvgIcon-root': { fontSize: 'inherit', color: 'text.secondary' },
          }}
        >
          {icon}
        </IconButton>
        {!isLast && (
          <Box // timeline line
            sx={{
              flex: '1 1 auto',
              width: '1px',
              backgroundColor: 'grey.400',
              minHeight: '24px',
              mt: 1,
            }}
          />
        )}
      </ListItemIcon>
      <ListItemText
        sx={{ my: 0 }}
        primary={<Typography sx={{ mb: 0.5 }}>{primaryText}</Typography>}
        secondary={
          <Stack
            spacing={0.5}
            sx={{ pb: 2 }}
          >
            {secondaryText && (
              <Typography
                color="text.secondary"
                sx={{ whiteSpace: 'pre-line' }}
              >
                {secondaryText}
              </Typography>
            )}
          </Stack>
        }
      />
    </ListItem>
  )
}

const ActivityLogDrawer = ({ onClose, bookingId, bookingStatus }: Props) => {
  const activityLogQuery = useActivityLogQuery(bookingId)

  return (
    <DrawerBase
      open
      onClose={onClose}
      header={
        <Stack spacing={2}>
          <Stack
            direction="row"
            justifyContent="space-between"
            alignItems="center"
          >
            <Typography variant="h6">Activity Log</Typography>
            <IconButton
              onClick={onClose}
              size="small"
            >
              <CloseIcon />
            </IconButton>
          </Stack>

          <Stack
            direction="row"
            spacing={1}
            alignItems="center"
          >
            <Typography color="text.secondary">Booking number: {bookingId}</Typography>
            <CarpoolStatusChip bookingStatusId={bookingStatus} />
          </Stack>
        </Stack>
      }
      PaperProps={{ sx: { width: '500px' } }}
    >
      {match(activityLogQuery)
        .with({ status: 'pending' }, () => <CircularProgressDelayedAbsolute />)
        .with({ status: 'error' }, () => null)
        .with({ status: 'success' }, ({ data }) => (
          <List sx={{ py: 0 }}>
            {data.activities.map((activity, index) => (
              <ActivityLogItem
                key={activity.timestamp.toMillis()}
                activity={activity}
                isLast={index === data.activities.length - 1}
              />
            ))}
          </List>
        ))
        .exhaustive()}
    </DrawerBase>
  )
}

export default ActivityLogDrawer
