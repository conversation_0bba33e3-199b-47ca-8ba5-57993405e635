import { useQuery } from '@tanstack/react-query'
import { DateTime } from 'luxon'
import { match } from 'ts-pattern'

import { makeQueryErrorHandlerWithToast } from 'api/helpers'
import type { PromiseResolvedType } from 'src/types'
import { createQuery } from 'src/util-functions/react-query-utils'

import { BookingStatus } from '../../../utils/constants'
import { generateMockActivityLog } from './activityLogMockData'

// Action types for different booking statuses
type RequestAction = {
  statusId: BookingStatus.BOOKING_STATUS_REQUESTED
}

type ApproveAction = {
  statusId: BookingStatus.BOOKING_STATUS_APPROVED
  assignedDriver: string
  assignedVehicleCommander: string
}

type DeclineAction = {
  statusId: BookingStatus.BOOKING_STATUS_DECLINED
  reason: string
  remarks?: string
}

type CancelAction = {
  statusId: BookingStatus.BOOKING_STATUS_CANCELLED
  reason: string
  remarks?: string
}

type ActivateAction = {
  statusId: BookingStatus.BOOKING_STATUS_ACTIVE
  actualPickupDateTime: string
}

type ReturnAction = {
  statusId: BookingStatus.BOOKING_STATUS_RETURNED
  actualDropoffDateTime: string
}

type ForceTerminateAction = {
  statusId: BookingStatus.BOOKING_STATUS_FORCE_TERMINATED
  reason: string
  remarks?: string
}

type EditAction = {
  statusId: 'EDIT' // Special status for edit actions
  changes: Array<{
    field: string
    previousValue: string | Date | number | undefined | null
    updatedValue: string | Date | number | undefined | null
  }>
}

export type ActivityAction =
  | RequestAction
  | ApproveAction
  | DeclineAction
  | CancelAction
  | ActivateAction
  | ReturnAction
  | ForceTerminateAction
  | EditAction

export type ActivityLogEntry = {
  bookingId: number
  timestamp: string // ISO string
  user: { id: string; name: string }
  action: ActivityAction
}

export declare namespace FetchActivityLog {
  type OutPut = {
    bookingId: number
    bookingStatus: string
    activities: Array<ActivityLogEntry>
  }
}

// Helper function to format values for display
const formatValue = (value: any): string => {
  if (value === null) return 'null'
  if (value === undefined) return 'undefined'
  if (value instanceof Date) {
    return DateTime.fromJSDate(value).toFormat('f')
  }
  if (typeof value === 'number') return value.toString()
  return String(value)
}

// Helper function to parse and format activity log entries
const parseActivityLogEntry = (entry: ActivityLogEntry) =>
  // Use pattern matching to handle different action types
  ({
    ...entry,
    timestamp: DateTime.fromISO(entry.timestamp),
    action: match(entry.action)
      .with({ statusId: 'EDIT' }, (editAction) => ({
        ...editAction,
        formattedChanges: editAction.changes.map((change) => ({
          ...change,
          formattedText: `Update ${change.field}: ${formatValue(
            change.previousValue,
          )} → ${formatValue(change.updatedValue)}`,
        })),
      }))
      .with({ statusId: BookingStatus.BOOKING_STATUS_ACTIVE }, (activateAction) => ({
        ...activateAction,
        formattedPickupTime: DateTime.fromISO(
          activateAction.actualPickupDateTime,
        ).toFormat('f'),
      }))
      .with({ statusId: BookingStatus.BOOKING_STATUS_RETURNED }, (returnAction) => ({
        ...returnAction,
        formattedDropoffTime: DateTime.fromISO(
          returnAction.actualDropoffDateTime,
        ).toFormat('f'),
      }))
      .with({ statusId: BookingStatus.BOOKING_STATUS_APPROVED }, (approveAction) => ({
        ...approveAction,
        // Format assignment details if needed
        formattedAssignmentDetails: [
          approveAction.assignedDriver &&
            `Assign driver: ${approveAction.assignedDriver}`,
          approveAction.assignedVehicleCommander &&
            `Assign vehicle commander: ${approveAction.assignedVehicleCommander}`,
        ]
          .filter(Boolean)
          .join('\n'),
      }))
      .with({ statusId: BookingStatus.BOOKING_STATUS_DECLINED }, (declineAction) => ({
        ...declineAction,
        formattedReason: `Remarks: ${declineAction.reason}`,
      }))
      .with(
        { statusId: BookingStatus.BOOKING_STATUS_FORCE_TERMINATED },
        (forceTerminateAction) => ({
          ...forceTerminateAction,
          formattedReason: `Reason: ${forceTerminateAction.reason}`,
        }),
      )
      .with(
        { statusId: BookingStatus.BOOKING_STATUS_REQUESTED },
        { statusId: BookingStatus.BOOKING_STATUS_CANCELLED },
        (action) => action,
      )
      .otherwise((action) => {
        // Handle any unknown status types gracefully
        console.warn(`Unknown action status: ${entry.action.statusId}`)
        return action
      }),
  })

async function getActivityLog(bookingId: number) {
  // TODO: Replace with actual API call
  // const result = await restGet<FetchActivityLog.OutPut>(
  //   `/booking/${bookingId}/activity-log`,
  // )
  // const rawData = result.data

  // For now, simulate API delay and get mock data
  await new Promise((resolve) => window.setTimeout(resolve, 500))
  const rawData = generateMockActivityLog(bookingId)

  // Parse and format the activity log entries
  return {
    ...rawData,
    activities: rawData.activities.map(parseActivityLogEntry),
  }
}

export const activityLogQueryKey = (bookingId: number) =>
  ['booking/activityLog', bookingId] as const

const activityLogQuery = (bookingId: number) =>
  createQuery({
    queryKey: activityLogQueryKey(bookingId),
    queryFn: () => getActivityLog(bookingId),
    enabled: !!bookingId,
    ...makeQueryErrorHandlerWithToast(),
  })

export const useActivityLogQuery = (bookingId: number) =>
  useQuery(activityLogQuery(bookingId))

export type ActivityLogResponse = PromiseResolvedType<typeof getActivityLog>
