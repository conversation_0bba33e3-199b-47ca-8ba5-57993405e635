import { useMutation } from '@tanstack/react-query'

import { useMutationErrorHandlerWithSnackbar } from 'api/helpers'
import { restPost } from 'src/api/rest-api-caller'
import { enqueueSnackbarWithCloseAction } from 'src/components/Snackbar/Notistack/utils'

import { ctIntl } from 'cartrack-ui-kit'
import useBookingMutationInvalidation from '../../../components/ScdfIssuanceRequestDrawer/api/useBookingMutationInvalidation'

export declare namespace ScdfCancelBooking {
  type Params = {
    bookingIds: Array<string>
    reasonId: number
    remarks: string
    clientUserId: string
  }

  type RequestPayload = {
    bookingIds: Array<string>
    bookingCancelReasonId: number
    bookingCancelNotes: string
    canceledClientUserId: string
  }

  type Response = {
    id: number
    message?: string
  }
}

function cancelBooking(
  params: ScdfCancelBooking.Params,
): Promise<ScdfCancelBooking.Response> {
  const requestPayload: ScdfCancelBooking.RequestPayload = {
    bookingIds: params.bookingIds,
    bookingCancelReasonId: params.reasonId,
    bookingCancelNotes: params.remarks || '',
    canceledClientUserId: params.clientUserId,
  }

  return restPost<ScdfCancelBooking.Response>(`/scdf/booking/cancel`, requestPayload)
}

const useCancelBookingMutation = () => {
  const invalidateQueries = useBookingMutationInvalidation()

  return useMutation({
    mutationFn: cancelBooking,
    onSuccess: () => {
      enqueueSnackbarWithCloseAction(
        ctIntl.formatMessage({
          id: 'carpool.list.cancelBookingSuccessMessage',
        }),
        { variant: 'success' },
      )
      invalidateQueries({ shouldInvalidateSpecificBooking: false })
    },
    ...useMutationErrorHandlerWithSnackbar(),
  })
}

export default useCancelBookingMutation
