import { useMutation } from '@tanstack/react-query'

import { restPatch } from 'src/api/rest-api-caller'
import { enqueueSnackbarWithCloseAction } from 'src/components/Snackbar/Notistack/utils'

import { ctIntl } from 'cartrack-ui-kit'
import useBookingMutationInvalidation from '../../../components/ScdfIssuanceRequestDrawer/api/useBookingMutationInvalidation'

export declare namespace ScdfActivateBooking {
  type Params = {
    bookingId: string
    pickUpTime: Date
    actualPickUpTime: Date
    vehicle: string
    driver: string
    vehicleCommander: string
    approver: string
  }
  type RequestPayload = {
    bookingId: string
    pickUpTime: string
    actualPickUpTime: string
    vehicleId: string
    clientDriverId: string
    vehicleCommanderClientUserId: string
    activatedClientUserId: string
  }
  type Response = {
    id: number
    message?: string
  }
}

function activateBooking(
  params: ScdfActivateBooking.Params,
): Promise<ScdfActivateBooking.Response> {
  const requestPayload: ScdfActivateBooking.RequestPayload = {
    bookingId: params.bookingId,
    pickUpTime: params.pickUpTime.toISOString(),
    actualPickUpTime: params.actualPickUpTime.toISOString(),
    vehicleId: params.vehicle,
    clientDriverId: params.driver,
    vehicleCommanderClientUserId: params.vehicleCommander,
    activatedClientUserId: params.approver,
  }

  return restPatch<ScdfActivateBooking.Response>(
    '/scdf/booking/activate',
    requestPayload,
  )
}

const useActivateBookingMutation = () => {
  const invalidateQueries = useBookingMutationInvalidation()

  return useMutation({
    mutationFn: activateBooking,
    onSuccess: () => {
      enqueueSnackbarWithCloseAction(
        ctIntl.formatMessage({
          id: 'carpool.list.activateBookingSuccessMessage',
        }),
        { variant: 'success' },
      )
      invalidateQueries({ shouldInvalidateSpecificBooking: false })
    },
    onError: (error) => {
      enqueueSnackbarWithCloseAction(
        error.message ??
          ctIntl.formatMessage({
            id: 'carpool.error.activateBookingGeneralMessage',
          }),
        { variant: 'error' },
      )
    },
  })
}

export default useActivateBookingMutation
