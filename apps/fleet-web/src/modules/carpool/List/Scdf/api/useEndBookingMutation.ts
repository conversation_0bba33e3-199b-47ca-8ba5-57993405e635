import { useMutation } from '@tanstack/react-query'

import { restPatch } from 'src/api/rest-api-caller'
import { enqueueSnackbarWithCloseAction } from 'src/components/Snackbar/Notistack/utils'

import { ctIntl } from 'cartrack-ui-kit'
import useBookingMutationInvalidation from '../../../components/ScdfIssuanceRequestDrawer/api/useBookingMutationInvalidation'

export declare namespace ScdfEndBooking {
  type Params = {
    bookingId: string
    dropOffTime: Date
    actualDropOffTime: Date
    vehicle: string
    driver: string
    vehicleCommander: string
    clientUserId: string
  }
  type RequestPayload = {
    bookingId: string
    dropOffTime: Date
    actualDropOffTime: Date
    vehicleId: string
    returnedClientDriverId: string
    returnedVehicleCommanderClientUserId: string
    completedClientUserId: string
  }
  type Response = {
    id: number
    message?: string
  }
}

function endBooking(params: ScdfEndBooking.Params): Promise<ScdfEndBooking.Response> {
  const requestPayload: ScdfEndBooking.RequestPayload = {
    bookingId: params.bookingId,
    dropOffTime: params.dropOffTime,
    actualDropOffTime: params.actualDropOffTime,
    vehicleId: params.vehicle,
    returnedClientDriverId: params.driver,
    returnedVehicleCommanderClientUserId: params.vehicleCommander,
    completedClientUserId: params.clientUserId,
  }

  return restPatch<ScdfEndBooking.Response>(`/scdf/booking/completed`, requestPayload)
}

const useEndBookingMutation = () => {
  const invalidateQueries = useBookingMutationInvalidation()

  return useMutation({
    mutationFn: endBooking,
    onSuccess: () => {
      enqueueSnackbarWithCloseAction(
        ctIntl.formatMessage({
          id: 'carpool.list.endBookingSuccessMessage',
        }),
        { variant: 'success' },
      )
      invalidateQueries({ shouldInvalidateSpecificBooking: false })
    },
    onError: (error) => {
      enqueueSnackbarWithCloseAction(
        error.message ??
          ctIntl.formatMessage({
            id: 'carpool.error.endBookingGeneralMessage',
          }),
        { variant: 'error' },
      )
    },
  })
}

export default useEndBookingMutation
