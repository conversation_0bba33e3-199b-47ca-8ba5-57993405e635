import { useQuery } from '@tanstack/react-query'

import { makeQueryErrorHandlerWithToast } from 'api/helpers'
import { restGet } from 'api/rest-api-caller'
import { createQuery } from 'src/util-functions/react-query-utils'

export declare namespace FetchCancelReasons {
  type CancelReason = {
    id: number
    title: string
  }

  type OutPut = { data: Array<CancelReason> }
}

async function getCancelReasons() {
  const result = await restGet<FetchCancelReasons.OutPut>(
    '/booking/cancel-booking-reasons',
  )

  return result.data
}

export const cancelReasonsQueryKey = () => ['booking/cancelReasons'] as const

const cancelReasonsQuery = () =>
  createQuery({
    queryKey: cancelReasonsQueryKey(),
    queryFn: getCancelReasons,
    ...makeQueryErrorHandlerWithToast(),
  })

export const useCancelReasonsQuery = () => useQuery(cancelReasonsQuery())
