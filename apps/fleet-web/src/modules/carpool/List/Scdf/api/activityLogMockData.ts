import { DateTime } from 'luxon'

import { BookingStatus } from '../../../utils/constants'
import type { ActivityLogEntry } from './useActivityLogQuery'

// Mock data generator for different status types
export const generateMockActivityLog = (bookingId: number) => {
  const baseTime = DateTime.now().minus({ days: 10 })

  const activities: Array<ActivityLogEntry> = [
    // 1. Request action
    {
      bookingId,
      timestamp: baseTime.plus({ days: 1 }).toISO(),
      user: {
        id: 'S987123697',
        name: '<PERSON>',
      },
      action: {
        statusId: BookingStatus.BOOKING_STATUS_REQUESTED,
      },
    },
    // 2. Approve action
    {
      bookingId,
      timestamp: baseTime.plus({ days: 2 }).toISO(),
      user: {
        id: 'S987123697',
        name: '<PERSON>',
      },
      action: {
        statusId: BookingStatus.BOOKING_STATUS_APPROVED,
        assignedDriver: '<PERSON>',
        assignedVehicleCommander: '<PERSON>',
      },
    },
    // 3. Edit action - first edit
    {
      bookingId,
      timestamp: baseTime.plus({ days: 3 }).toISO(),
      user: {
        id: 'S987123697',
        name: '<PERSON> Hariyanto Sutanto',
      },
      action: {
        statusId: 'EDIT',
        changes: [
          {
            field: 'Driver',
            previousValue: 'Handy Bandy',
            updatedValue: 'Harris Muhammad',
          },
          {
            field: 'Pick-up Date',
            previousValue: new Date('2025-03-04T10:00:00Z'),
            updatedValue: new Date('2025-03-04T09:00:00Z'),
          },
          {
            field: 'Passenger Count',
            previousValue: 2,
            updatedValue: 3,
          },
          {
            field: 'Special Requirements',
            previousValue: null,
            updatedValue: 'Wheelchair accessible',
          },
        ],
      },
    },
    // 4. Activate action
    {
      bookingId,
      timestamp: baseTime.plus({ days: 4 }).toISO(),
      user: {
        id: 'S987123697',
        name: 'Ham',
      },
      action: {
        statusId: BookingStatus.BOOKING_STATUS_ACTIVE,
        actualPickupDateTime: '2025-03-04T10:23:00Z',
      },
    },
    // 5. Return action
    {
      bookingId,
      timestamp: baseTime.plus({ days: 5 }).toISO(),
      user: {
        id: 'S987123697',
        name: 'Mario Hen',
      },
      action: {
        statusId: BookingStatus.BOOKING_STATUS_RETURNED,
        actualDropoffDateTime: '2025-02-10T16:20:00Z',
      },
    },
    // 6. Edit action - second edit
    {
      bookingId,
      timestamp: baseTime.plus({ days: 6 }).toISO(),
      user: {
        id: 'S987123697',
        name: 'Mario Hariyanto Sutanto',
      },
      action: {
        statusId: 'EDIT',
        changes: [
          {
            field: 'Drop-off Time',
            previousValue: '10 Feb 2025, 16:00',
            updatedValue: '10 Feb 2025, 16:20',
          },
          {
            field: 'Cost Estimate',
            previousValue: undefined,
            updatedValue: 45.5,
          },
        ],
      },
    },
    // 7. Decline action (example)
    {
      bookingId,
      timestamp: baseTime.plus({ days: 7 }).toISO(),
      user: {
        id: 'SF0000123',
        name: 'Herlando Marlando',
      },
      action: {
        statusId: BookingStatus.BOOKING_STATUS_DECLINED,
        reason: 'no available vehicle',
        remarks: 'Vehicle was in maintenance',
      },
    },
    // 8. Cancel action (example)
    {
      bookingId,
      timestamp: baseTime.plus({ days: 8 }).toISO(),
      user: {
        id: 'SF0000123',
        name: 'Herlando Marlando',
      },
      action: {
        statusId: BookingStatus.BOOKING_STATUS_CANCELLED,
        reason: 'no available vehicle',
        remarks: 'Vehicle was in maintenance',
      },
    },
    // 9. Force terminate action (example)
    {
      bookingId,
      timestamp: baseTime.plus({ days: 9 }).toISO(),
      user: {
        id: 'SYSTEM',
        name: 'The system',
      },
      action: {
        statusId: BookingStatus.BOOKING_STATUS_FORCE_TERMINATED,
        reason: 'Booking not activated',
        remarks: 'Booking was not activated within 24 hours',
      },
    },
  ]

  return {
    bookingId,
    bookingStatus: 'RETURNED',
    activities: activities.sort(
      (a, b) =>
        DateTime.fromISO(b.timestamp).toMillis() -
        DateTime.fromISO(a.timestamp).toMillis(),
    ),
  }
}
