import { useCallback, useEffect, useMemo, useState, type ReactElement } from 'react'
import { isEmpty, isNil } from 'lodash'
import {
  Box,
  Button,
  ContainerWithTabsForDataGrid,
  DataGridAsTabItem,
  GridActionsCellItem,
  GridLogicOperator,
  LinearProgress,
  MenuItem,
  MenuList,
  Popover,
  Tooltip,
  Typography,
  useDataGridColumnHelper,
  type GridColDef,
  type GridFilterModel,
  type GridRowSelectionModel,
  type GridSortModel,
} from '@karoo-ui/core'
import AddIcon from '@mui/icons-material/Add'
import CloseIcon from '@mui/icons-material/Close'
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined'
import KeyIcon from '@mui/icons-material/Key'
import ThreeDotsIcon from '@mui/icons-material/MoreVert'
import { DateTime } from 'luxon'
import PopupState, { bindPopover, bindTrigger } from 'material-ui-popup-state'
import { useHistory, useRouteMatch } from 'react-router'
import * as R from 'remeda'
import { match } from 'ts-pattern'
import type { ValueOf } from 'type-fest'

import { buildRouteQueryString } from 'api/utils'
import { getAuthenticatedUser, getSettings_UNSAFE } from 'duxs/user'
import PageWithMainTableContainer from 'src/components/_containers/PageWithMainTable'
import { DETAILS_PREFIX } from 'src/modules/app/components/routes/carpool'
import { UserDataGridWithSavedSettingsOnIDB } from 'src/modules/components/connected/UserDataGridWithSavedSettingsOnIDB'
import { useTypedSelector } from 'src/redux-hooks'
import KarooToolbar from 'src/shared/data-grid/KarooToolbar'
import {
  mapFilterItemToServerFilter_Date,
  mapFilterItemToServerFilter_String,
} from 'src/shared/data-grid/server-client/utils'
import { Array_filterMap } from 'src/util-functions/performance-critical-utils'

import { ctIntl } from 'cartrack-ui-kit'
import CarpoolStatusChip from '../../components/CarpoolStatusChip'
import { issuanceRequestSearchParamsSchema } from '../../components/ScdfIssuanceRequestDrawer/schema'
import StatBar from '../../components/StatBar'
import StatusInfoDrawer from '../../components/StatusInfoDrawer'
import useCarpoolOptionsQuery, {
  type FetchCarpoolOptionsParsedData,
} from '../../queries/useCarpoolOptionsQuery'
import {
  useApproveActiveRule,
  useKeyCollectionRule,
  useRuleLoadingStatus,
} from '../../Settings/Rules/api/queries'
import { BookingStatus, CustomTabs } from '../../utils/constants'
import {
  CarpoolOptionsContext,
  customTabAndStatusMapping,
  LIST_TABS,
  TAB_OPTIONS,
} from '../constants'
import useBookingSubmissionsQuery, {
  type FormattedSubmitInfo,
} from '../Spf/api/useBookingSubmissionsQuery'
import BookingDetailsDrawer from '../Spf/components/BookingDetailsDrawer' // TODO:
import BookingSubmissionsChip from '../Spf/components/BookingSubmissionsChip'
import type { FormPossibleValues as KeyCollectionFormPossibleValues } from '../Spf/components/KeyCollectionModal'
import KeyReturnModal from '../Spf/components/KeyReturnModal'
import useServerPaginationIssuanceListQuery, {
  fetchIssuanceListFilterModelSchema,
  type Booking,
  type FetchIssuanceListFilterModelSchemaSelf,
  type FetchServerPaginationIssuanceList,
  type Metric,
} from './api/useServerPaginationIssuanceListQuery'
import ActivateBookingModal from './components/ActivateBookingModal'
import ActivityLogDrawer from './components/ActivityLogDrawer'
import CancelBookingModal from './components/CancelBookingModal'
import EndBookingModal from './components/EndBookingModal'
import ForceTerminateBookingModal from './components/ForceTerminateBookingModal'

const metricConfig: Array<{
  key: Metric
  label: string
  tab?: CustomTabs
  id?: BookingStatus
}> = [
  { key: 'TOTAL', label: 'Total' },
  {
    key: 'ACTIVE',
    label: 'Active',
    tab: CustomTabs.ACTIVE,
    id: BookingStatus.BOOKING_STATUS_ACTIVE,
  },
  {
    key: 'ACTIVE_LATE',
    label: 'Active Late',
    tab: CustomTabs.ACTIVE_LATE,
    id: BookingStatus.BOOKING_STATUS_ACTIVE_LATE,
  },
  {
    key: 'REQUESTED',
    label: 'Requested',
    tab: CustomTabs.REQUESTED,
    id: BookingStatus.BOOKING_STATUS_REQUESTED,
  },
  {
    key: 'APPROVED',
    label: 'Approved',
    tab: CustomTabs.APPROVED,
    id: BookingStatus.BOOKING_STATUS_APPROVED,
  },
  {
    key: 'DECLINED',
    label: 'Declined',
    tab: CustomTabs.DECLINED,
    id: BookingStatus.BOOKING_STATUS_DECLINED,
  },
  {
    key: 'CANCELLED',
    label: 'Cancelled',
    tab: CustomTabs.CANCELLED,
    id: BookingStatus.BOOKING_STATUS_CANCELLED,
  },
  {
    key: 'FREE',
    label: 'Free',
    tab: CustomTabs.FREE,
    id: BookingStatus.BOOKING_STATUS_FREE,
  },
  {
    key: 'RETURNED',
    label: 'Returned',
    tab: CustomTabs.RETURNED,
    id: BookingStatus.BOOKING_STATUS_RETURNED,
  },
  {
    key: 'RETURNED_LATE',
    label: 'Returned Late',
    tab: CustomTabs.RETURNED_LATE,
    id: BookingStatus.BOOKING_STATUS_RETURNED_LATE,
  },
  {
    key: 'FORCE_TERMINATED',
    label: 'Force Terminated',
    tab: CustomTabs.FORCE_TERMINATED,
    id: BookingStatus.BOOKING_STATUS_FORCE_TERMINATED,
  },
] as const

const columnsGetters = {
  bookingNumber: (booking: IssuanceItem) => booking.id,
  vehicle: (booking: IssuanceItem) => booking.vehicleRegistration ?? '',
  journeyType: (booking: IssuanceItem) => booking.journeyType ?? '',
  driver: (booking: IssuanceItem) => booking.driverName ?? '',
  driverEmail: (booking: IssuanceItem) => booking.driverEmail ?? '',
  vehicleType: (booking: IssuanceItem) => booking.vehicleType ?? '',
  vehicleCommander: (booking: IssuanceItem) => booking.vehicleCommander ?? '',
  purpose: (booking: IssuanceItem) => booking.purpose ?? '',
  requestor: (booking: IssuanceItem) => booking.requestor,
  requestDate: (booking: IssuanceItem) =>
    booking.requestDate ? new Date(booking.requestDate) : null,
  startDate: (booking: IssuanceItem) =>
    booking.startDate ? new Date(booking.startDate) : null,
  pickUpAt: (booking: IssuanceItem) =>
    booking.pickupIgnitionTime ? new Date(booking.pickupIgnitionTime) : null,
  pickupLocation: (booking: IssuanceItem) => booking.pickupLocation ?? '',
  endDate: (booking: IssuanceItem) =>
    booking.endDate ? new Date(booking.endDate) : null,
  returnedAt: (booking: IssuanceItem) => {
    if (
      [
        BookingStatus.BOOKING_STATUS_RETURNED,
        BookingStatus.BOOKING_STATUS_RETURNED_LATE,
        BookingStatus.BOOKING_STATUS_FORCE_TERMINATED,
      ].includes(booking.statusId.toString() as BookingStatus)
    ) {
      return booking.returnedIgnitionTime
        ? new Date(booking.returnedIgnitionTime)
        : null
    } else {
      return null
    }
  },
  returnedLocation: (booking: IssuanceItem) => booking.dropoffLocation ?? '',
  statusId: (booking: IssuanceItem) => booking.statusId.toString(),
  status: (booking: IssuanceItem) => booking.status,
  approvedBy: (booking: IssuanceItem) => booking.approvedBy,
  declinedBy: (booking: IssuanceItem) => booking.declinedBy,
}

type IssuanceItem = Booking & {
  formSubmissions: Array<FormattedSubmitInfo>
}

const filterableColumnIds = R.values(fetchIssuanceListFilterModelSchema.items).map(
  (item) => item.shape.field.value,
)
type FilterableColumnId = (typeof filterableColumnIds)[number]

const sortableColumnIds = [
  'startDate',
  'pickUpAt',
  'requestDate',
  'endDate',
  'returnedAt',
] as const
type SortableColumnId = (typeof sortableColumnIds)[number]

type DataGridSortModel = [
  {
    field: SortableColumnId
    sort: 'asc' | 'desc'
  },
]

const columnsIds = {
  bookingNumber: 'bookingNumber',
  vehicle: 'vehicle',
  journeyType: 'journeyType',
  driver: 'driver',
  driverEmail: 'driverEmail',
  vehicleType: 'vehicleType',
  vehicleCommander: 'vehicleCommander',
  purpose: 'purpose',
  requestor: 'requestor',
  requestDate: 'requestDate',
  startDate: 'startDate',
  pickUpAt: 'pickUpAt',
  pickupLocation: 'pickupLocation',
  endDate: 'endDate',
  returnedAt: 'returnedAt',
  returnedLocation: 'returnedLocation',
  status: 'status',
  approvedBy: 'approvedBy',
  declinedBy: 'declinedBy',
  actions: 'actions',
} as const satisfies Record<
  FilterableColumnId | SortableColumnId,
  FilterableColumnId | SortableColumnId
> &
  Record<string, unknown>

type DataGridFilterModel = FetchIssuanceListFilterModelSchemaSelf

const ScdfList = () => {
  const { path } = useRouteMatch()
  const history = useHistory()
  const columnHelper = useDataGridColumnHelper<IssuanceItem>({ filterMode: 'client' })

  const [currentModal, setCurrentModal] = useState<
    | {
        type: 'key_return'
        data: {
          bookingId: string
          initialValues: KeyCollectionFormPossibleValues
        }
      }
    | {
        type: 'force_terminate_booking' | 'cancel_booking'
        data: { bookingIds: Array<string | number> }
      }
    | {
        type: 'end_booking'
        data: {
          bookingId: string
          bookingStatus: BookingStatus
          initialValues: {
            dropOffTime: string
            vehicle: number
            driver: string
            vehicleCommander: string
          }
        }
      }
    | {
        type: 'activate_booking'
        data: {
          bookingId: string
          initialValues: {
            pickUpTime: string
            vehicle: number
            driver: string
            vehicleCommander: string
          }
        }
      }
    | null
  >(null)

  const [currentDrawer, setCurrentDrawer] = useState<
    | {
        type: 'booking_details'
        data: {
          bookingId: number
          vehicleTimelineParams: {
            vehicleId: number
            startDate: string
            endDate: string
          }
        }
      }
    | {
        type: 'activity_log'
        data: {
          bookingId: number
          bookingStatus: BookingStatus
        }
      }
    | null
  >(null)

  const [issuanceList, setIssuanceList] = useState<Array<IssuanceItem>>([])
  const [tabs, setTabs] = useState<
    ReadonlyArray<{
      label: string
      value: ValueOf<typeof LIST_TABS>
      icon?: ReactElement
      iconPosition?: 'bottom' | 'top' | 'end' | 'start' | undefined
    }>
  >(TAB_OPTIONS)
  const [currentTab, setCurrentTab] = useState(TAB_OPTIONS[0].value)
  const [customSelection, setCustomSelection] = useState<CustomTabs | ''>('')
  const [multiSelectedBookingIds, setMultiSelectedBookingIds] = useState<
    Array<string | number>
  >([])

  const [isStatusInfoDrawerOpen, setIsStatusInfoDrawerOpen] = useState(false)

  const handleStatusInfoClick = () => {
    setIsStatusInfoDrawerOpen(true)
  }

  const handleStatusInfoClose = () => {
    setIsStatusInfoDrawerOpen(false)
  }

  const [paginationModel, setPaginationModel] = useState({
    page: 0,
    pageSize: 25,
  })
  const [sortModel, setSortModel] = useState<DataGridSortModel>([
    { field: columnsIds.startDate, sort: 'asc' },
  ])
  const [filterModel, setFilterModel] = useState<DataGridFilterModel>(() => ({
    items: [],
    logicOperator: GridLogicOperator.Or,
    quickFilterValues: [],
  }))

  const {
    carpoolAppName,
    carpoolApproveBookings,
    carpoolEditBookings,
    carpoolChangeBookingToActive,
    carpoolCancelBooking,
    carpoolForceTerminateBooking,
    isAdmin,
  } = useTypedSelector(getSettings_UNSAFE)
  const { cuid: clientUserId } = useTypedSelector(getAuthenticatedUser)

  const queryParam = useMemo(
    () =>
      ({
        pagination: {
          offset: paginationModel.page * paginationModel.pageSize,
          pageSize: paginationModel.pageSize,
        },
        sort: sortModel.length > 0 ? sortModel[0] : null,
        filter: {
          searchTextFilterValues: filterModel.quickFilterValues,
          logicOperator: filterModel.logicOperator,
          items: Array_filterMap(filterModel.items, (item, { RemoveSymbol }) => {
            switch (item.field) {
              case 'vehicle': {
                const { value, operator } = item
                const serverFilter = mapFilterItemToServerFilter_String({
                  field: columnsIds.vehicle,
                  value,
                  operator,
                  case: 'insensitive',
                })

                return serverFilter === 'TO_IGNORE' ? RemoveSymbol : serverFilter
              }
              case 'driver': {
                const { value, operator } = item
                const serverFilter = mapFilterItemToServerFilter_String({
                  field: columnsIds.driver,
                  value,
                  operator,
                  case: 'insensitive',
                })

                return serverFilter === 'TO_IGNORE' ? RemoveSymbol : serverFilter
              }
              case 'driverEmail': {
                const { value, operator } = item
                const serverFilter = mapFilterItemToServerFilter_String({
                  field: columnsIds.driverEmail,
                  value,
                  operator,
                  case: 'insensitive',
                })

                return serverFilter === 'TO_IGNORE' ? RemoveSymbol : serverFilter
              }
              case 'vehicleType': {
                const { value, operator } = item
                const serverFilter = mapFilterItemToServerFilter_String({
                  field: columnsIds.vehicleType,
                  value,
                  operator,
                  case: 'insensitive',
                })

                return serverFilter === 'TO_IGNORE' ? RemoveSymbol : serverFilter
              }
              case 'purpose': {
                const { value, operator } = item
                const serverFilter = mapFilterItemToServerFilter_String({
                  field: columnsIds.purpose,
                  value,
                  operator,
                  case: 'insensitive',
                })

                return serverFilter === 'TO_IGNORE' ? RemoveSymbol : serverFilter
              }
              case 'vehicleCommander': {
                const { value, operator } = item
                const serverFilter = mapFilterItemToServerFilter_String({
                  field: columnsIds.vehicleCommander,
                  value,
                  operator,
                  case: 'insensitive',
                })

                return serverFilter === 'TO_IGNORE' ? RemoveSymbol : serverFilter
              }
              case 'requestor': {
                const { value, operator } = item
                const serverFilter = mapFilterItemToServerFilter_String({
                  field: columnsIds.requestor,
                  value,
                  operator,
                  case: 'insensitive',
                })

                return serverFilter === 'TO_IGNORE' ? RemoveSymbol : serverFilter
              }
              case 'requestDate': {
                const { value, operator } = item
                const serverFilter = mapFilterItemToServerFilter_Date({
                  field: columnsIds.requestDate,
                  value,
                  operator,
                  precision: 'day',
                })
                return serverFilter === 'TO_IGNORE' ? RemoveSymbol : serverFilter
              }
            }
          }),
        },
        statusIds: match(currentTab)
          .with(LIST_TABS.SCHEDULED, () =>
            [
              BookingStatus.BOOKING_STATUS_APPROVED,
              BookingStatus.BOOKING_STATUS_REQUESTED,
            ].join(','),
          )
          .with(LIST_TABS.IN_PROGRESS, () =>
            [
              BookingStatus.BOOKING_STATUS_ACTIVE,
              BookingStatus.BOOKING_STATUS_ACTIVE_LATE,
            ].join(','),
          )
          .with(LIST_TABS.HISTORY, () =>
            [
              BookingStatus.BOOKING_STATUS_CANCELLED,
              BookingStatus.BOOKING_STATUS_DECLINED,
              BookingStatus.BOOKING_STATUS_RETURNED,
              BookingStatus.BOOKING_STATUS_RETURNED_LATE,
              BookingStatus.BOOKING_STATUS_FORCE_TERMINATED,
            ].join(','),
          )
          .with(LIST_TABS.CUSTOM, () =>
            customSelection ? customTabAndStatusMapping[customSelection] : null,
          )
          .otherwise(() => null),
      }) satisfies Parameters<typeof useServerPaginationIssuanceListQuery>[0],
    [
      currentTab,
      customSelection,
      filterModel.items,
      filterModel.logicOperator,
      filterModel.quickFilterValues,
      paginationModel.page,
      paginationModel.pageSize,
      sortModel,
    ],
  )

  const issuanceListQuery = useServerPaginationIssuanceListQuery(queryParam)

  const bookingSubmissionsQuery = useBookingSubmissionsQuery()
  const bookingOptionQuery = useCarpoolOptionsQuery()
  const keyCollectionRule = useKeyCollectionRule()
  const activeBookingRule = useApproveActiveRule()
  const isRuleFetching = useRuleLoadingStatus()

  useEffect(() => {
    if (issuanceListQuery.data) {
      const submissionMap = bookingSubmissionsQuery.data
      setIssuanceList(
        issuanceListQuery.data.bookings.map((issuance) => ({
          ...issuance,
          formSubmissions: submissionMap ? submissionMap.get(issuance.id) ?? [] : [],
        })),
      )
    }
  }, [issuanceListQuery.data, bookingSubmissionsQuery.data])

  const canApproveBooking = useCallback(
    (pendingManagers: Array<Array<string>>) =>
      isAdmin ||
      isEmpty(pendingManagers) ||
      pendingManagers[0].includes(clientUserId ?? ''),
    [clientUserId, isAdmin],
  )

  const renderForceTerminateAction = useCallback(
    (bookingId: number) => (
      <GridActionsCellItem
        label={ctIntl.formatMessage({ id: 'tfms.list.forceTerminate' })}
        onClick={() => {
          setCurrentModal({
            type: 'force_terminate_booking',
            data: {
              bookingIds: [bookingId.toString()],
            },
          })
        }}
        showInMenu
        disabled={!carpoolForceTerminateBooking}
      />
    ),
    [carpoolForceTerminateBooking],
  )

  const commonIssuanceActions = useCallback(
    ({
      bookingId,
      bookingStatus,
      showCancelAction = true,
      mode = 'edit',
    }: {
      bookingId: number
      bookingStatus: BookingStatus
      showCancelAction?: boolean
      mode?: 'view' | 'edit'
    }) => [
      <GridActionsCellItem
        key={mode}
        label={ctIntl.formatMessage({
          id: mode === 'view' ? 'tfms.list.viewBooking' : 'tfms.list.editBooking',
        })}
        onClick={() =>
          history.push(
            `${path}/${DETAILS_PREFIX}?${buildRouteQueryString({
              schema: issuanceRequestSearchParamsSchema,
              searchParams: { type: mode, id: bookingId.toString() },
            })}`,
          )
        }
        disabled={!carpoolEditBookings}
        showInMenu
      />,
      <GridActionsCellItem
        key="duplicate"
        label={ctIntl.formatMessage({ id: 'Duplicate' })}
        onClick={() =>
          history.push(
            `${path}/${DETAILS_PREFIX}?${buildRouteQueryString({
              schema: issuanceRequestSearchParamsSchema,
              searchParams: { type: 'duplicate', id: bookingId.toString() },
            })}`,
          )
        }
        showInMenu
      />,
      <GridActionsCellItem
        key="activityLog"
        label="Activity Log"
        onClick={() => {
          setCurrentDrawer({
            type: 'activity_log',
            data: {
              bookingId,
              bookingStatus,
            },
          })
        }}
        showInMenu
      />,
      showCancelAction ? (
        <GridActionsCellItem
          key="cancel"
          label={ctIntl.formatMessage({ id: 'carpool.list.cancelBooking' })}
          onClick={() =>
            setCurrentModal({
              type: 'cancel_booking',
              data: { bookingIds: [bookingId.toString()] },
            })
          }
          showInMenu
          disabled={!carpoolCancelBooking}
        />
      ) : (
        <></>
      ),
    ],
    [carpoolCancelBooking, carpoolEditBookings, history, path],
  )

  const generateIssuanceActions = useCallback(
    (row: IssuanceItem) => {
      const {
        statusId,
        id: bookingId,
        driverId,
        vehicleId,
        vehicleTypeId,
        startDate,
        endDate,
        keyCollectionDate,
        keyReturnDate,
        pickupIgnitionTime,
        returnedIgnitionTime,
      } = row

      const initialValuesForKeyCollection: KeyCollectionFormPossibleValues = {
        driver: driverId,
        vehicle: vehicleId,
        vehicleType: vehicleTypeId,
      }

      const actualStartAndEndTime = match(statusId.toString())
        .with(
          BookingStatus.BOOKING_STATUS_ACTIVE,
          BookingStatus.BOOKING_STATUS_ACTIVE_LATE,
          () => ({
            startDate: pickupIgnitionTime || startDate,
            endDate: DateTime.now().toString(),
          }),
        )
        .with(
          BookingStatus.BOOKING_STATUS_RETURNED,
          BookingStatus.BOOKING_STATUS_RETURNED_LATE,
          () => ({
            startDate: pickupIgnitionTime || startDate,
            endDate: returnedIgnitionTime || endDate,
          }),
        )
        .otherwise(() => ({
          startDate: pickupIgnitionTime || startDate,
          endDate,
        }))

      const vehicleTimelineParams = { vehicleId, ...actualStartAndEndTime }

      return match(statusId.toString() as BookingStatus)
        .with(BookingStatus.BOOKING_STATUS_REQUESTED, () => [
          <GridActionsCellItem
            key="approve"
            label={ctIntl.formatMessage({ id: 'Approve/Reject' })}
            onClick={() =>
              history.push(
                `${path}/${DETAILS_PREFIX}?${buildRouteQueryString({
                  schema: issuanceRequestSearchParamsSchema,
                  searchParams: {
                    type: 'approve',
                    id: bookingId.toString(),
                  },
                })}`,
              )
            }
            disabled={
              !carpoolApproveBookings || !canApproveBooking(row.pendingManagers)
            }
            showInMenu
          />,
          ...commonIssuanceActions({
            bookingId,
            bookingStatus: statusId.toString() as BookingStatus,
          }),
        ])
        .with(BookingStatus.BOOKING_STATUS_APPROVED, () => [
          <GridActionsCellItem
            key="changeToActive"
            label={ctIntl.formatMessage({ id: 'carpool.list.changeToActive' })}
            onClick={() =>
              setCurrentModal({
                type: 'activate_booking',
                data: {
                  bookingId: bookingId.toString(),
                  initialValues: {
                    pickUpTime: row.startDate,
                    vehicle: row.vehicleId,
                    driver: row.driverId || '',
                    vehicleCommander: row.vehicleCommanderId,
                  },
                },
              })
            }
            showInMenu
            disabled={!carpoolChangeBookingToActive}
          />,
          ...commonIssuanceActions({
            bookingId,
            bookingStatus: statusId.toString() as BookingStatus,
          }),
        ])
        .with(
          BookingStatus.BOOKING_STATUS_ACTIVE,
          BookingStatus.BOOKING_STATUS_ACTIVE_LATE,
          () => {
            const additionalAction = (() => {
              if (isRuleFetching) {
                return <></>
              }
              return match(activeBookingRule)
                .with('checklist', 'geofence', () =>
                  renderForceTerminateAction(bookingId),
                )
                .with('keyCollection', () => (
                  <>
                    {renderForceTerminateAction(bookingId)}
                    {keyCollectionRule ? (
                      <Tooltip
                        title={ctIntl.formatMessage({
                          id: 'carpool.list.returnKey',
                        })}
                        arrow
                        key="returnKey"
                      >
                        <span>
                          <GridActionsCellItem
                            icon={<KeyIcon />}
                            label=""
                            onClick={() => {
                              setCurrentModal({
                                type: 'key_return',
                                data: {
                                  bookingId: bookingId.toString(),
                                  initialValues: initialValuesForKeyCollection,
                                },
                              })
                            }}
                          />
                        </span>
                      </Tooltip>
                    ) : (
                      <></>
                    )}
                  </>
                ))
                .with('manual', () => renderForceTerminateAction(bookingId))
                .exhaustive()
            })()
            return [
              <GridActionsCellItem
                key="endBooking"
                label={ctIntl.formatMessage({ id: 'tfms.list.endBooking.title' })}
                onClick={() =>
                  setCurrentModal({
                    type: 'end_booking',
                    data: {
                      bookingId: bookingId.toString(),
                      bookingStatus: statusId.toString() as BookingStatus,
                      initialValues: {
                        dropOffTime: row.endDate,
                        vehicle: row.vehicleId,
                        driver: row.driverId || '',
                        vehicleCommander: row.vehicleCommanderId,
                      },
                    },
                  })
                }
                showInMenu
              />,
              additionalAction,
              ...commonIssuanceActions({
                bookingId,
                bookingStatus: statusId.toString() as BookingStatus,
                showCancelAction: false,
              }),
            ]
          },
        )
        .with(
          BookingStatus.BOOKING_STATUS_CANCELLED,
          BookingStatus.BOOKING_STATUS_DECLINED,
          () =>
            commonIssuanceActions({
              bookingId,
              bookingStatus: statusId.toString() as BookingStatus,
              showCancelAction: false,
              mode: 'view',
            }),
        )
        .with(
          BookingStatus.BOOKING_STATUS_RETURNED,
          BookingStatus.BOOKING_STATUS_RETURNED_LATE,
          BookingStatus.BOOKING_STATUS_FORCE_TERMINATED,
          () => [
            keyCollectionRule ? (
              <Tooltip
                title={ctIntl.formatMessage({
                  id: isNil(keyReturnDate)
                    ? 'carpool.list.returnKey'
                    : 'carpool.list.keyIsReturned',
                })}
                key="returnKey"
              >
                <span>
                  <GridActionsCellItem
                    icon={<KeyIcon />}
                    label=""
                    onClick={() => {
                      setCurrentModal({
                        type: 'key_return',
                        data: {
                          bookingId: bookingId.toString(),
                          initialValues: initialValuesForKeyCollection,
                        },
                      })
                    }}
                    disabled={!isNil(keyReturnDate) || isNil(keyCollectionDate)}
                  />
                </span>
              </Tooltip>
            ) : (
              <></>
            ),
            ...commonIssuanceActions({
              bookingId,
              bookingStatus: statusId.toString() as BookingStatus,
              showCancelAction: false,
            }),
            <GridActionsCellItem
              key="bookingDetail"
              label={ctIntl.formatMessage({ id: 'tfms.list.bookingDetail' })}
              onClick={() =>
                setCurrentDrawer({
                  type: 'booking_details',
                  data: {
                    bookingId,
                    vehicleTimelineParams,
                  },
                })
              }
              showInMenu
            />,
          ],
        )
        .otherwise(() => [])
    },
    [
      activeBookingRule,
      canApproveBooking,
      carpoolApproveBookings,
      carpoolChangeBookingToActive,
      commonIssuanceActions,
      history,
      isRuleFetching,
      keyCollectionRule,
      path,
      renderForceTerminateAction,
    ],
  )

  const columns = useMemo((): Array<GridColDef<IssuanceItem>> => {
    const base: Array<GridColDef<IssuanceItem>> = [
      columnHelper.number((_, row) => columnsGetters.bookingNumber(row), {
        field: columnsIds.bookingNumber,
        headerName: ctIntl.formatMessage({ id: 'carpool.list.bookingNumber' }),
        align: 'left',
      }),
      columnHelper.string((_, row) => columnsGetters.journeyType(row), {
        field: columnsIds.journeyType,
        headerName: ctIntl.formatMessage({ id: 'carpool.journeyType' }),
      }),
      columnHelper.string((_, row) => columnsGetters.vehicle(row), {
        field: columnsIds.vehicle,
        headerName: ctIntl.formatMessage({ id: 'Vehicle' }),
      }),
      columnHelper.string((_, row) => columnsGetters.driver(row), {
        field: columnsIds.driver,
        headerName: ctIntl.formatMessage({ id: 'Driver' }),
      }),
      columnHelper.string((_, row) => columnsGetters.driverEmail(row), {
        field: columnsIds.driverEmail,
        headerName: ctIntl.formatMessage({ id: 'Email' }),
      }),
      columnHelper.string((_, row) => columnsGetters.vehicleType(row), {
        field: columnsIds.vehicleType,
        headerName: ctIntl.formatMessage({ id: 'carpool.vehicleCategory' }),
      }),
      columnHelper.string((_, row) => columnsGetters.vehicleCommander(row), {
        field: columnsIds.vehicleCommander,
        headerName: ctIntl.formatMessage({ id: 'carpool.vehicleCommander' }),
      }),
      columnHelper.string((_, row) => columnsGetters.purpose(row), {
        field: columnsIds.purpose,
        headerName: ctIntl.formatMessage({ id: 'carpool.list.purpose' }),
      }),
      columnHelper.string((_, row) => columnsGetters.requestor(row), {
        field: columnsIds.requestor,
        headerName: ctIntl.formatMessage({ id: 'carpool.bookingDetails.requestor' }),
      }),
      columnHelper.dateTime({
        field: columnsIds.requestDate,
        headerName: ctIntl.formatMessage({ id: 'Request Date' }),
        valueGetter: (_, row) => columnsGetters.requestDate(row),
        filterOperators: columnHelper.utils
          .getGridDateColumnOperators({ showTime: false })
          .filter((operator) => operator.value === 'range'),
      }),
      columnHelper.dateTime({
        field: columnsIds.startDate,
        headerName: ctIntl.formatMessage({ id: 'Pick Up At' }),
        valueGetter: (_, row) => columnsGetters.startDate(row),
      }),
      columnHelper.dateTime({
        field: columnsIds.pickUpAt,
        headerName: ctIntl.formatMessage({ id: 'Actual Pick Up At' }),
        valueGetter: (_, row) => columnsGetters.pickUpAt(row),
      }),
      columnHelper.string((_, row) => columnsGetters.pickupLocation(row), {
        field: columnsIds.pickupLocation,
        headerName: ctIntl.formatMessage({ id: 'Pick-Up from' }),
      }),
      columnHelper.dateTime({
        field: columnsIds.endDate,
        headerName: ctIntl.formatMessage({ id: 'Drop-off At' }),
        valueGetter: (_, row) => columnsGetters.endDate(row),
      }),
      columnHelper.dateTime({
        field: columnsIds.returnedAt,
        headerName: ctIntl.formatMessage({ id: 'Actual Drop-off At' }),
        valueGetter: (_, row) => columnsGetters.returnedAt(row),
      }),
      columnHelper.string((_, row) => columnsGetters.returnedLocation(row), {
        field: columnsIds.returnedLocation,
        headerName: ctIntl.formatMessage({ id: 'Drop-off To' }),
      }),
      columnHelper.valueGetter((_, row) => columnsGetters.status(row), {
        field: columnsIds.status,
        headerName: ctIntl.formatMessage({ id: 'Status' }),
        renderCell: ({ row }) => (
          <CarpoolStatusChip
            bookingStatusId={columnsGetters.statusId(row) as BookingStatus}
          />
        ),
        minWidth: 150,
      }),
      columnHelper.string((_, row) => columnsGetters.approvedBy(row), {
        field: columnsIds.approvedBy,
        headerName: ctIntl.formatMessage({ id: 'Approved By' }),
      }),
      columnHelper.string((_, row) => columnsGetters.declinedBy(row), {
        field: columnsIds.declinedBy,
        headerName: ctIntl.formatMessage({ id: 'Declined By' }),
      }),
      {
        field: columnsIds.actions,
        type: 'actions',
        headerName: ctIntl.formatMessage({ id: 'Actions' }),
        minWidth: 120,
        align: 'right',
        getActions: ({ row }) => [
          <BookingSubmissionsChip
            key="submissions-info"
            formSubmissions={row.formSubmissions}
            isLoading={bookingSubmissionsQuery.isPending}
          />,
          ...generateIssuanceActions(row),
        ],
      },
    ]

    return base.map((column) => ({
      ...column,
      sortable: sortableColumnIds.includes(column.field as SortableColumnId),
      filterable: filterableColumnIds.includes(column.field as FilterableColumnId),
    }))
  }, [columnHelper, bookingSubmissionsQuery.isPending, generateIssuanceActions])

  const onFilterModelChange = async (newFilterModel: GridFilterModel) => {
    setFilterModel(newFilterModel as DataGridFilterModel)

    // We set the filter model anyways but we try to throw an error while in dev for developers to fix it
    if (ENV.NODE_ENV === 'development') {
      // Might throw an error if the filodel is invalid (which is what we want)
      fetchIssuanceListFilterModelSchema.self.parse(newFilterModel)
      return
    }
    return
  }

  const handleCloseCustomTab = useCallback(() => {
    setCurrentTab(tabs[0].value)
    setTabs((prev) => {
      const newTabs = [...prev]
      newTabs.pop()

      return newTabs
    })
    setCustomSelection('')
  }, [tabs])

  const handleCustomSelect = useCallback(
    (target: CustomTabs) => {
      setTabs((prev) => [
        ...prev,
        {
          label: `${ctIntl.formatMessage({ id: 'Custom' })} - ${ctIntl.formatMessage({
            id: target,
          })}`,
          value: 'custom',
          icon: (
            <CloseIcon
              onClick={handleCloseCustomTab}
              fontSize="small"
            />
          ),
          iconPosition: 'end',
        },
      ])

      setCurrentTab('custom')
      setCustomSelection(target)
    },
    [handleCloseCustomTab],
  )

  const getBulkActionOptions = () => {
    if (isEmpty(multiSelectedBookingIds)) {
      return []
    }

    const allStatus = issuanceList
      .filter((booking) => multiSelectedBookingIds.includes(booking.id))
      .map((booking) => columnsGetters.statusId(booking))

    if (!isEmpty(allStatus)) {
      if (
        allStatus.every((s) =>
          [
            BookingStatus.BOOKING_STATUS_ACTIVE,
            BookingStatus.BOOKING_STATUS_ACTIVE_LATE,
          ].includes(s as BookingStatus),
        )
      ) {
        return [
          <MenuItem
            key="force_terminate_booking"
            onClick={() =>
              setCurrentModal({
                type: 'force_terminate_booking',
                data: {
                  bookingIds: [...multiSelectedBookingIds],
                },
              })
            }
            disabled={!carpoolForceTerminateBooking}
          >
            {ctIntl.formatMessage({
              id: 'tfms.list.forceTerminate',
            })}
          </MenuItem>,
        ]
      }

      if (
        allStatus.every(
          (s) => BookingStatus.BOOKING_STATUS_REQUESTED === (s as BookingStatus),
        )
      ) {
        const canApproveBookings = issuanceList
          .filter((booking) => multiSelectedBookingIds.includes(booking.id))
          .every((booking) => canApproveBooking(booking.pendingManagers))
        return [
          <MenuItem
            key="cancel_booking"
            onClick={() =>
              setCurrentModal({
                type: 'cancel_booking',
                data: {
                  bookingIds: [...multiSelectedBookingIds],
                },
              })
            }
            disabled={!carpoolApproveBookings || !canApproveBookings}
          >
            {ctIntl.formatMessage({ id: 'carpool.list.cancelBooking' })}
          </MenuItem>,
        ]
      }
    }

    return []
  }

  const handleSelectionModelChange = (newSelectionModel: GridRowSelectionModel) => {
    setMultiSelectedBookingIds(newSelectionModel as Array<string>)
  }

  const resetTablePagination = () => {
    setPaginationModel({ ...paginationModel, page: 0 })
  }

  const onSortModelChange = (_newSortModel: GridSortModel) => {
    const newSortModel = _newSortModel as DataGridSortModel

    resetTablePagination()
    setSortModel(newSortModel)
  }

  const listMetrics = useMemo(() => {
    if (!isEmpty(issuanceListQuery.data?.metrics)) {
      const statusMetrics = (
        issuanceListQuery.data as FetchServerPaginationIssuanceList.Return
      ).metrics.statusMetrics

      return statusMetrics
        .map((metric) => {
          const status = metricConfig.find(
            ({ id }) => id === metric.statusId.toString(),
          )

          if (status) {
            return {
              label: status.label,
              value: metric.count,
              // oxlint-disable-next-line no-non-null-assertion
              ...(status.tab && { onClick: () => handleCustomSelect(status.tab!) }),
            }
          }

          return null
        })
        .filter(Boolean) as Array<{
        label: string
        value: number
        onClick?: () => void
      }>
    }

    return []
  }, [issuanceListQuery.data, handleCustomSelect])

  const shouldShowCheckboxSelection =
    currentTab !== LIST_TABS.HISTORY &&
    ![
      CustomTabs.DECLINED,
      CustomTabs.CANCELLED,
      CustomTabs.RETURNED,
      CustomTabs.RETURNED_LATE,
    ].includes(customSelection as CustomTabs)

  const carpoolOptionsContextValue = useMemo(
    () => ({
      carpoolOptionsData:
        bookingOptionQuery.data ?? ({} as FetchCarpoolOptionsParsedData),
    }),
    [bookingOptionQuery.data],
  )

  return (
    <CarpoolOptionsContext.Provider value={carpoolOptionsContextValue}>
      <PageWithMainTableContainer>
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            mb: 4,
          }}
        >
          <Typography variant="h5">
            {ctIntl.formatMessage(
              { id: 'carpool.list.header' },
              { values: { carpoolAppName } },
            )}
          </Typography>
          <Button
            startIcon={<AddIcon />}
            onClick={() =>
              history.push(
                `${path}/${DETAILS_PREFIX}?${buildRouteQueryString({
                  schema: issuanceRequestSearchParamsSchema,
                  searchParams: { type: 'add' },
                })}`,
              )
            }
            color="primary"
            variant="contained"
          >
            {ctIntl.formatMessage(
              { id: 'carpool.newCarpoolRequest' },
              { values: { carpoolAppName } },
            )}
          </Button>
        </Box>
        <StatBar
          stats={listMetrics}
          isClickable={!customSelection}
          isLoading={issuanceListQuery.isPending}
        />

        <ContainerWithTabsForDataGrid
          renderTabs={() => (
            <ContainerWithTabsForDataGrid.Tabs
              value={currentTab}
              onChange={(_e, newValue) => setCurrentTab(newValue)}
            >
              {tabs.map(({ label, value, icon, iconPosition }) => (
                <ContainerWithTabsForDataGrid.Tab
                  key={value}
                  label={label}
                  value={value}
                  sx={{
                    height: '48px',
                    minHeight: '48px',
                  }}
                  {...(icon ? { icon, iconPosition } : {})}
                />
              ))}
            </ContainerWithTabsForDataGrid.Tabs>
          )}
        >
          <UserDataGridWithSavedSettingsOnIDB<IssuanceItem>
            Component={DataGridAsTabItem}
            dataGridId="currentIssuance"
            disableVirtualization
            disableRowSelectionOnClick
            loading={issuanceListQuery.isPending}
            rowCount={issuanceListQuery.data?.metrics?.total ?? 0}
            pagination
            filterDebounceMs={500} // since were doing server side-filtering, we can not afford a lower debounce (to not doo too many requests)
            pageSizeOptions={[10, 25, 50]}
            paginationMode="server"
            paginationModel={paginationModel}
            onPaginationModelChange={setPaginationModel}
            sortingMode="server"
            sortModel={sortModel}
            onSortModelChange={onSortModelChange}
            filterMode="server"
            filterModel={filterModel}
            onFilterModelChange={onFilterModelChange}
            autosizeOnMount
            rows={issuanceList}
            getRowId={(row) => row.id}
            columns={columns}
            checkboxSelection={shouldShowCheckboxSelection}
            onRowSelectionModelChange={handleSelectionModelChange}
            rowSelectionModel={multiSelectedBookingIds}
            slots={{
              toolbar: KarooToolbar,
              loadingOverlay: LinearProgress,
            }}
            slotProps={{
              toolbar: KarooToolbar.createProps({
                slots: {
                  searchFilter: { show: true },
                  settingsButton: { show: true },
                  filterButton: { show: true },
                },
                extraContent: {
                  right: (
                    <>
                      {shouldShowCheckboxSelection && (
                        <PopupState
                          variant="popover"
                          popupId="bulk-action"
                        >
                          {(popupState) => {
                            const builkActionOptions = getBulkActionOptions()
                            return (
                              <div>
                                <Button
                                  size="small"
                                  variant="outlined"
                                  color="secondary"
                                  sx={{
                                    width: '30px',
                                    height: '30px',
                                    minWidth: 'auto',
                                  }}
                                  disabled={isEmpty(builkActionOptions)}
                                  {...bindTrigger(popupState)}
                                >
                                  <ThreeDotsIcon />
                                </Button>
                                <Popover
                                  {...bindPopover(popupState)}
                                  anchorOrigin={{
                                    vertical: 'bottom',
                                    horizontal: 'center',
                                  }}
                                  transformOrigin={{
                                    vertical: 'top',
                                    horizontal: 'center',
                                  }}
                                >
                                  <MenuList>{builkActionOptions}</MenuList>
                                </Popover>
                              </div>
                            )
                          }}
                        </PopupState>
                      )}
                      <Button
                        sx={{ color: 'primary.dark' }}
                        variant="text"
                        startIcon={<InfoOutlinedIcon />}
                        size="small"
                        onClick={handleStatusInfoClick}
                      >
                        status info
                      </Button>
                    </>
                  ),
                },
              }),
              pagination: { showFirstButton: true, showLastButton: true },
            }}
          />
        </ContainerWithTabsForDataGrid>

        {isStatusInfoDrawerOpen && <StatusInfoDrawer onClose={handleStatusInfoClose} />}

        {match(currentModal)
          .with(null, () => null)
          .with({ type: 'key_return' }, ({ data }) => (
            <KeyReturnModal
              onClose={() => setCurrentModal(null)}
              bookingId={data.bookingId}
              initialValues={data.initialValues}
            />
          ))
          .with({ type: 'cancel_booking' }, ({ data }) => (
            <CancelBookingModal
              onClose={() => setCurrentModal(null)}
              bookingIds={data.bookingIds as Array<string>}
            />
          ))
          .with({ type: 'force_terminate_booking' }, ({ data }) => (
            <ForceTerminateBookingModal
              onClose={() => setCurrentModal(null)}
              bookingIds={data.bookingIds as Array<string>}
            />
          ))
          .with({ type: 'end_booking' }, ({ data }) => (
            <EndBookingModal
              onClose={() => setCurrentModal(null)}
              bookingId={data.bookingId}
              bookingStatus={data.bookingStatus}
              initialValues={data.initialValues}
            />
          ))
          .with({ type: 'activate_booking' }, ({ data }) => (
            <ActivateBookingModal
              onClose={() => setCurrentModal(null)}
              bookingId={data.bookingId}
              initialValues={data.initialValues}
            />
          ))
          .exhaustive()}

        {currentDrawer?.type === 'booking_details' && (
          <BookingDetailsDrawer
            onClose={() => setCurrentDrawer(null)}
            bookingId={currentDrawer.data.bookingId}
            vehicleTimelineParams={currentDrawer.data.vehicleTimelineParams}
          />
        )}

        {currentDrawer?.type === 'activity_log' && (
          <ActivityLogDrawer
            onClose={() => setCurrentDrawer(null)}
            bookingId={currentDrawer.data.bookingId}
            bookingStatus={currentDrawer.data.bookingStatus}
          />
        )}
      </PageWithMainTableContainer>
    </CarpoolOptionsContext.Provider>
  )
}

export default ScdfList
