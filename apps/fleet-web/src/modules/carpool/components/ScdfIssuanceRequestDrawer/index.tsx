import { CircularProgressDelayedAbsolute } from '@karoo-ui/core'
import { match } from 'ts-pattern'

import { getAuthenticatedUser } from 'duxs/user'
import { useValidatedSearchParams } from 'src/hooks/useValidatedSearchParams'
import { useTypedSelector } from 'src/redux-hooks'

import type { BookingStatus } from '../../utils/constants'
import useApproveBookingMutation from './api/useApproveBookingMutation'
import useBookingAttachmentsQuery from './api/useBookingAttachmentsQuery'
import useBookingDetailsQuery from './api/useBookingDetailsQuery'
import useCreateBookingMutation from './api/useCreateBookingMutation'
import useEditBookingMutation from './api/useEditBookingMutation'
import useRejectBookingMutation from './api/useRejectBookingMutation'
import CreateEditBookingDrawer from './CreateEditBookingDrawer'
import { issuanceRequestSearchParamsSchema, type ValidSchema } from './schema'
import type { RejectBookingFormData } from './types'
import { mapBookingToFormValues } from './utils'
import ViewApproveBookingDrawer from './ViewApproveBookingDrawer'

type IssuanceRequestDrawerWrapperProps = {
  onClose: () => void
}

const ScdfIssuanceRequestDrawerWrapper = ({
  onClose,
}: IssuanceRequestDrawerWrapperProps) => {
  const validatedParams = useValidatedSearchParams(
    () => issuanceRequestSearchParamsSchema,
  )

  return match(validatedParams)
    .with({ status: 'invalid' }, () => null)
    .with({ status: 'valid', data: { type: 'add' } }, () => (
      <AddBookingWrapper onClose={onClose} />
    ))
    .with({ status: 'valid', data: { type: 'edit' } }, ({ data: { id } }) => (
      <EditBookingWrapper
        bookingId={Number.parseInt(id)}
        onClose={onClose}
      />
    ))
    .with({ status: 'valid', data: { type: 'approve' } }, ({ data: { id } }) => (
      <ApproveBookingWrapper
        bookingId={Number.parseInt(id)}
        onClose={onClose}
      />
    ))
    .with({ status: 'valid', data: { type: 'view' } }, ({ data: { id } }) => (
      <ViewBookingWrapper
        bookingId={Number.parseInt(id)}
        onClose={onClose}
      />
    ))
    .with({ status: 'valid', data: { type: 'duplicate' } }, ({ data: { id } }) => (
      <DuplicateBookingWrapper
        bookingId={Number.parseInt(id)}
        onClose={onClose}
      />
    ))
    .exhaustive()
}

type DuplicateBookingWrapperProps = {
  bookingId: number
  onClose: () => void
}

const DuplicateBookingWrapper = ({
  bookingId,
  onClose,
}: DuplicateBookingWrapperProps) => {
  const { cuid: clientUserId } = useTypedSelector(getAuthenticatedUser)
  const bookingAttachmentsQuery = useBookingAttachmentsQuery(bookingId)
  const bookingDetailsQuery = useBookingDetailsQuery(bookingId)
  const createMutation = useCreateBookingMutation()

  const handleDuplicateSubmit = (formValues: ValidSchema) => {
    createMutation.mutate(
      { ...formValues, requestClientUserId: clientUserId || '' },
      {
        onSuccess() {
          onClose()
        },
      },
    )
  }

  return match(bookingDetailsQuery)
    .with({ status: 'pending' }, () => <CircularProgressDelayedAbsolute />)
    .with({ status: 'error' }, () => null)
    .with({ status: 'success' }, ({ data: bookingDetails }) => (
      <CreateEditBookingDrawer
        mode="duplicate"
        onClose={onClose}
        onSubmit={handleDuplicateSubmit}
        onSubmitLoading={createMutation.isPending}
        initialValues={mapBookingToFormValues(bookingDetails)}
        attachmentsQuery={bookingAttachmentsQuery}
      />
    ))
    .exhaustive()
}

type AddBookingWrapperProps = {
  onClose: () => void
}

const AddBookingWrapper = ({ onClose }: AddBookingWrapperProps) => {
  const { cuid: clientUserId } = useTypedSelector(getAuthenticatedUser)
  const createBookingMutation = useCreateBookingMutation()

  const handleAddSubmit = (formValues: ValidSchema) => {
    createBookingMutation.mutate(
      { ...formValues, requestClientUserId: clientUserId || '' },
      {
        onSuccess() {
          onClose()
        },
      },
    )
  }

  return (
    <CreateEditBookingDrawer
      mode="create"
      onClose={onClose}
      onSubmit={handleAddSubmit}
      onSubmitLoading={createBookingMutation.isPending}
    />
  )
}

type EditBookingWrapperProps = {
  bookingId: number
  onClose: () => void
}

const EditBookingWrapper = ({ bookingId, onClose }: EditBookingWrapperProps) => {
  const bookingDetailsQuery = useBookingDetailsQuery(bookingId)
  const bookingAttachmentsQuery = useBookingAttachmentsQuery(bookingId)
  const editBookingMutation = useEditBookingMutation()

  const handleEditSubmit = (formValues: ValidSchema) => {
    editBookingMutation.mutate(
      {
        ...formValues,
        bookingId,
        requestClientUserId: bookingDetailsQuery.data?.requestClientUserId ?? '',
      },
      {
        onSuccess() {
          onClose()
        },
      },
    )
  }

  return match(bookingDetailsQuery)
    .with({ status: 'pending' }, () => <CircularProgressDelayedAbsolute />)
    .with({ status: 'error' }, () => null)
    .with({ status: 'success' }, ({ data: bookingDetails }) => (
      <CreateEditBookingDrawer
        mode="edit"
        onClose={onClose}
        onSubmit={handleEditSubmit}
        onSubmitLoading={editBookingMutation.isPending}
        initialValues={mapBookingToFormValues(bookingDetails)}
        bookingId={bookingDetails.id}
        bookingStatusId={bookingDetails.statusId.toString() as BookingStatus}
        isKeyCollected={!!bookingDetails.keyReturnTs}
        attachmentsQuery={bookingAttachmentsQuery}
      />
    ))
    .exhaustive()
}

type ApproveBookingWrapperProps = {
  bookingId: number
  onClose: () => void
}

const ApproveBookingWrapper = ({ bookingId, onClose }: ApproveBookingWrapperProps) => {
  const { cuid: clientUserId } = useTypedSelector(getAuthenticatedUser)
  const bookingDetailsQuery = useBookingDetailsQuery(bookingId)
  const bookingAttachmentsQuery = useBookingAttachmentsQuery(bookingId)
  const approveBookingMutation = useApproveBookingMutation()
  const rejectBookingMutation = useRejectBookingMutation()

  const handleApprove = (formValues: ValidSchema) => {
    approveBookingMutation.mutate(
      {
        ...formValues,
        bookingId,
        requestClientUserId: bookingDetailsQuery.data?.requestClientUserId ?? '',
      },
      {
        onSuccess() {
          onClose()
        },
      },
    )
  }

  const handleReject = (rejectData: RejectBookingFormData) => {
    rejectBookingMutation.mutate(
      {
        bookingId,
        userId: clientUserId || '',
        reasonId: rejectData.reasonId as number,
        remarks: rejectData.remarks,
      },
      {
        onSuccess() {
          onClose()
        },
      },
    )
  }

  return match(bookingDetailsQuery)
    .with({ status: 'pending' }, () => <CircularProgressDelayedAbsolute />)
    .with({ status: 'error' }, () => null)
    .with({ status: 'success' }, ({ data: bookingDetails }) => (
      <ViewApproveBookingDrawer
        mode="approve"
        onClose={onClose}
        onSubmit={handleApprove}
        onReject={handleReject}
        onApprovalLoading={approveBookingMutation.isPending}
        onRejectLoading={rejectBookingMutation.isPending}
        initialValues={mapBookingToFormValues(bookingDetails)}
        bookingId={bookingDetails.id}
        bookingStatusId={bookingDetails.statusId.toString() as BookingStatus}
        isKeyCollected={!!bookingDetails.keyReturnTs}
        attachmentsQuery={bookingAttachmentsQuery}
      />
    ))
    .exhaustive()
}

type ViewBookingWrapperProps = {
  bookingId: number
  onClose: () => void
}

const ViewBookingWrapper = ({ bookingId, onClose }: ViewBookingWrapperProps) => {
  const bookingDetailsQuery = useBookingDetailsQuery(bookingId)
  const bookingAttachmentsQuery = useBookingAttachmentsQuery(bookingId)

  // No-op function for view mode since form won't be submitted
  const handleViewSubmit = () => {
    // This should never be called in view mode
  }

  return match(bookingDetailsQuery)
    .with({ status: 'pending' }, () => <CircularProgressDelayedAbsolute />)
    .with({ status: 'error' }, () => null)
    .with({ status: 'success' }, ({ data: bookingDetails }) => (
      <ViewApproveBookingDrawer
        mode="view"
        onClose={onClose}
        onSubmit={handleViewSubmit}
        initialValues={mapBookingToFormValues(bookingDetails)}
        bookingId={bookingDetails.id}
        bookingStatusId={bookingDetails.statusId.toString() as BookingStatus}
        isKeyCollected={!!bookingDetails.keyReturnTs}
        attachmentsQuery={bookingAttachmentsQuery}
      />
    ))
    .exhaustive()
}

export default ScdfIssuanceRequestDrawerWrapper
