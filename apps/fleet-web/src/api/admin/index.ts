import { camelCase, has, reduce } from 'lodash'
import * as R from 'remeda'
import { match } from 'ts-pattern'
import type { ValueOf } from 'type-fest'

import {
  companyDepartmentIdsSchema,
  driverIdSchema,
  type ApiOutputVehicleId,
  type VehicleId,
} from 'api/types'
import { parseApiOutputVehicleId } from 'api/utils'
import type {
  UserProfileSchema,
  UserProfileWithCostsSchema,
} from 'src/modules/admin/manage-users/EditUser/profile'
import type { BEBoolean, BEBooleanShort, FixMeAny } from 'src/types'
import { Array_filterMap } from 'src/util-functions/performance-critical-utils'

import { isNilOrEmptyString, isTrue } from 'cartrack-utils'
import apiCaller, { apiCallerNoX } from '../api-caller'
import {
  CLIENT_DATA_PERMISSION_OBJ,
  type ClientDataPermissionKeyType,
  type ClientDataPermissionValueType,
  type Ct_fleet_update_admin_data_access_permissions,
  type Ct_fleet_update_client_data_access_permissions,
  type FetchClientPermissions,
  type FetchUserDriverGroupAccess,
  type FetchUserDriverGroups,
  type FetchUserDrivers,
  type FetchUserFeatures,
  type FetchUserInfo,
  type FetchUsers,
  type FetchUserVehicleGroupAccess,
  type FetchUserVehicleGroups,
  type UpdateSubUser,
  type UpdateUserDriverGroupAccess,
  type UpdateUserDrivers,
  type UpdateUserInfo,
  type UpdateUserVehicleGroupAccess,
  type UpdateUserVehicleGroups,
  type UserNormalizedStatus,
  type UserPermissionsDataType,
  type ViewEditPermission,
} from './types'

export function parseUserAssignedVehicles(
  rawVehicles: Array<{ vehicle_id: ApiOutputVehicleId }>,
) {
  // Will be used as a Set
  const assignedVehicles: { [id: VehicleId]: boolean } = {}
  for (const vehicle of rawVehicles) {
    assignedVehicles[parseApiOutputVehicleId(vehicle.vehicle_id)] = true
  }
  return assignedVehicles
}

export const isWithCostsSchema = (
  values: UserProfileSchema,
): values is UserProfileWithCostsSchema => has(values, 'languageId')

const getNormalizedProfileValuesIfIsWithCostsSchema = (values: UserProfileSchema) =>
  isWithCostsSchema(values)
    ? {
        parent_id: values.parentUserId,
        language_id: values.languageId,
        name: values.name,
        user_role_id: values.user_role_id,
      }
    : null

export const generateUserStatus = ({
  clientUserStatus,
  isLocked,
}: {
  clientUserStatus: string | null
  isLocked: BEBoolean
}): UserNormalizedStatus => {
  const isActive = (clientUserStatus || '').includes('10')
  if (isActive) {
    return isTrue(isLocked) ? 'blocked' : 'active'
  }
  return 'deactivated'
}

export function parseFetchUserInfoResponse(rawUser: FetchUserInfo.ApiOutput) {
  const getPermission = (
    viewPermission: BEBooleanShort,
    editPermission: BEBooleanShort,
  ) =>
    [isTrue(viewPermission) ? 'view' : '', isTrue(editPermission) ? 'edit' : ''].join(
      ':',
    ) as ViewEditPermission

  const parseDeparmentIds = companyDepartmentIdsSchema.safeParse(rawUser.department_ids)

  const user = {
    id: rawUser.client_user_id,
    profile: {
      username: rawUser.user_name,
      email: rawUser.e_mail,
      cellPhone: rawUser.cell_number,
      costsFields:
        rawUser.language_id !== undefined &&
        rawUser.name !== undefined &&
        rawUser.parent_client_user_id !== undefined &&
        rawUser.user_role_id
          ? {
              languageId: rawUser.language_id,
              name: rawUser.name,
              parentUserId: rawUser.parent_client_user_id,
              user_role_id: rawUser.user_role_id,
            }
          : undefined,
      departmentIds: parseDeparmentIds.success ? parseDeparmentIds.data : null,
      clientUserRoleId: rawUser.client_user_role_id,
    },
    status: generateUserStatus({
      clientUserStatus: rawUser.client_user_statuses,
      isLocked: rawUser.is_locked,
    }),

    /* We should look into a way to remove this. It should not be needed anymore  */
    baseFeatures: {
      subUserGeofenceSharing: getPermission(
        rawUser.allow_subuser_tosee_mainuser_geofences,
        rawUser.allow_subuser_toedit_mainuser_geofences,
      ),
      mainUserGeofenceSharing: getPermission(
        rawUser.allow_mainuser_tosee_subuser_geofences,
        rawUser.allow_mainuser_toedit_subuser_geofences,
      ),
      subUserPOISharing: getPermission(
        rawUser.allow_subuser_tosee_mainuser_poi,
        rawUser.allow_subuser_toedit_mainuser_poi,
      ),
      mainUserPOISharing: getPermission(
        rawUser.allow_mainuser_tosee_subuser_poi,
        rawUser.allow_mainuser_toedit_subuser_poi,
      ),
    },
  }

  return {
    user,
    dropdownLists: {
      clientUsers: rawUser.clientUsers
        ? rawUser.clientUsers.map((user) => ({
            value: user.client_user_id,
            name: user.user_name,
          }))
        : [],
      userRoles: rawUser.userRoles
        ? rawUser.userRoles.map((user) => ({
            value: user.user_role_id,
            name: user.tag,
          }))
        : [],
    },
  }
}

export function parseUsersList(rawUsers: Array<FetchUsers.ApiOutput>) {
  return rawUsers.map((rawUser) => ({
    id: rawUser.client_user_id,
    username: rawUser.user_name,
    email: rawUser.e_mail,
    cellPhone: rawUser.cell_number,
    status: generateUserStatus({
      clientUserStatus: rawUser.client_user_statuses,
      isLocked: rawUser.is_locked,
    }),
    departmentIds: rawUser.department_ids,
    carpoolApproveBookings: rawUser.carpool_approve_bookings,
    carpoolDeclineBookings: rawUser.carpool_decline_bookings,
    carpoolEditBookings: rawUser.carpool_edit_bookings,
  }))
}

export function parseFetchUserVehicleGroupsResponse(
  groups: Array<FetchUserVehicleGroups.ApiOutput>,
) {
  return groups.map((group) => ({
    id: group.group_vehicle_id,
    name: group.group_vehicle_name,
    permissions: group.permissions,
    vehicles: group.vehicles.map((v) => ({
      id: parseApiOutputVehicleId(v.vehicle_id),
      name: v.vehicle_name,
      registration: v.registration,
    })),
  }))
}
function parseFetchUserDriverGroupsResponse(
  groups: Array<FetchUserDriverGroups.ApiOutput>,
) {
  return groups.map((group) => ({
    id: group.group_driver_id,
    name: group.group_driver_name,
    permissions: group.permissions,
    drivers: Array_filterMap(group.drivers, (v, { RemoveSymbol }) => {
      const idResult = driverIdSchema.safeParse(v.client_driver_id)
      if (!idResult.success) {
        return RemoveSymbol
      }

      return {
        id: idResult.data,
        name: v.driver_name,
        registration: v.registration,
      }
    }),
  }))
}

export function parseFetchUserDriversResponse(
  drivers: Array<FetchUserDrivers.ApiOutput>,
) {
  return drivers.map((driver) => ({
    id: driver.client_driver_id,
    name: driver.driver_name,
    driverSurname: driver.driver_surname,
    driverGroups: driver.groupNames,
    permissions: driver.permissions,
  }))
}

export function parseFetchUserVehicleGroupAccessResponse(
  groups: Array<FetchUserVehicleGroupAccess.ApiOutput>,
) {
  return groups.map((group) => ({
    id: group.group_vehicle_id,
    name: group.group_vehicle_name,
    hasAccess: isTrue(group.has_access),
    vehicles: group.vehicles.map((v) => ({
      id: parseApiOutputVehicleId(v.vehicle_id),
      name: v.vehicle_name,
      registration: v.registration,
    })),
  }))
}

export function parseFetchUserDriverGroupAccessResponse(
  groups: Array<FetchUserDriverGroupAccess.ApiOutput>,
) {
  return groups.map((group) => ({
    id: group.group_driver_id,
    name: group.group_driver_name,
    hasAccess: isTrue(group.has_access),
    drivers: Array_filterMap(group.drivers, (v, { RemoveSymbol }) => {
      const idResult = driverIdSchema.safeParse(v.client_driver_id)
      if (!idResult.success) {
        return RemoveSymbol
      }
      return {
        id: idResult.data,
        name: v.driver_name,
        registration: v.registration,
      }
    }),
  }))
}

export function parseFetchClientPermissionResponse(
  result: FetchClientPermissions.ApiOutput,
) {
  return {
    adminDriverPermission:
      CLIENT_DATA_PERMISSION_OBJ[result.admin_driver_permission_id],
    adminDriverGroupsPermission:
      CLIENT_DATA_PERMISSION_OBJ[result.admin_driver_groups_permission_id],
    adminPoiPermission: CLIENT_DATA_PERMISSION_OBJ[result.admin_poi_permission_id],
    adminGeofencePermission:
      CLIENT_DATA_PERMISSION_OBJ[result.admin_geofence_permission_id],
    // driverPermission:
    //   res.client_driver_permission_id === null
    //     ? SELECT_BY_DRIVER_VALUE
    //     : CLIENT_DATA_PERMISSION_OBJ[result.ct_client_user_driver_permission_id],
    poiPermission: CLIENT_DATA_PERMISSION_OBJ[result.client_poi_permission_id],
    geofencePermission:
      CLIENT_DATA_PERMISSION_OBJ[result.client_geofence_permission_id],
  }
}

export function parseUserFeatures(
  apiFeatures: FetchUserFeatures.ApiOutput,
  {
    userSettings,
    listGeofencesPermissionSetting,
  }: {
    userSettings: Record<string, unknown>
    listGeofencesPermissionSetting: boolean
  },
) {
  // Should be removed in the future and put in the back-end
  function shouldSettingBeShownToUser(
    settingName: string,
    userSettings: Record<string, unknown>,
  ) {
    // Don't show user_importData as this will be dynamically assigned when updating to the endpoint "updateUserAclFeatures"
    if (settingName === 'user_importData') {
      return false
    }

    const permissionSettingValue = userSettings[camelCase(settingName)]

    return permissionSettingValue ?? true
  }

  const featuresData: {
    // Normalized settings
    settings: Record<string, { name: string; description: string; value: boolean }>
    // Each category only contains reference to settings names
    categories: Record<keyof typeof apiFeatures, Array<string>>
  } = {
    settings: {},
    categories: {} as Record<keyof typeof apiFeatures, Array<string>>,
  }

  return {
    featuresData: reduce(
      apiFeatures,
      (acc, featureCategory, untypedFeatureKey) => {
        const featureKey = untypedFeatureKey as keyof typeof apiFeatures
        const categorySettingsNames: Array<string> = []

        for (const setting of featureCategory) {
          if (shouldSettingBeShownToUser(setting.settingsName, userSettings)) {
            categorySettingsNames.push(setting.settingsName)
            acc.settings[setting.settingsName] = {
              name: setting.settingsName,
              description: isNilOrEmptyString(setting.settingsTranslationId)
                ? setting.settingsDescription
                : setting.settingsTranslationId,
              value: Boolean(setting.settingsValue),
            }
          }
        }

        acc.categories[featureKey] = categorySettingsNames

        return acc
      },
      featuresData,
    ),
    listFeatures: Array_filterMap(
      [
        {
          id: 'vehicles',
          flag: 'listVehicles',
        },
        {
          id: 'drivers',
          flag: 'listDrivers',
        },
        {
          id: 'landmarks',
          flag: 'listLandmarks',
        },
        listGeofencesPermissionSetting
          ? ({
              id: 'geofences',
            } as const)
          : null,
        {
          id: 'trailers',
          flag: 'listTrailers',
        },
      ] as const,
      (item, { RemoveSymbol }) => {
        if (!item) {
          return RemoveSymbol
        }

        if (item.flag && !userSettings[item.flag]) {
          return RemoveSymbol
        }

        return item
      },
    ),
  }
}

function normalizeUserPermission(
  permission: ClientDataPermissionValueType,
): ClientDataPermissionKeyType {
  return match<typeof permission, ClientDataPermissionKeyType>(permission)
    .with('hide', () => '0')
    .with('view', () => '1')
    .with('edit', () => '7')
    .exhaustive()
}

export function normalizePermissions(
  permissions: UpdateUserVehicleGroups.Group['permissions'],
) {
  return match<typeof permissions, PermissionLookupValueOfType>(permissions)
    .with({ view: false, edit: false, remove: false }, () => PermissionLookup[''])
    .with({ view: true, edit: false, remove: false }, () => PermissionLookup.view)
    .with({ view: false, edit: true, remove: false }, () => PermissionLookup.edit)
    .with({ view: false, edit: false, remove: true }, () => PermissionLookup.remove)
    .with({ view: true, edit: true, remove: false }, () => PermissionLookup.view_edit)
    .with({ view: true, edit: false, remove: true }, () => PermissionLookup.view_remove)
    .with({ view: false, edit: true, remove: true }, () => PermissionLookup.edit_remove)
    .with(
      { view: true, edit: true, remove: true },
      () => PermissionLookup.view_edit_remove,
    )
    .exhaustive()
}

export const parsePermissions = (permissionInNum: PermissionLookupValueOfType) =>
  match<PermissionLookupValueOfType, UpdateUserVehicleGroups.Group['permissions']>(
    permissionInNum,
  )
    .with(PermissionLookup[''], () => ({ view: false, edit: false, remove: false }))
    .with(PermissionLookup.view, () => ({ view: true, edit: false, remove: false }))
    .with(PermissionLookup.edit, () => ({ view: false, edit: true, remove: false }))
    .with(PermissionLookup.remove, () => ({ view: false, edit: false, remove: true }))
    .with(PermissionLookup.view_edit, () => ({ view: true, edit: true, remove: false }))
    .with(PermissionLookup.view_remove, () => ({
      view: true,
      edit: false,
      remove: true,
    }))
    .with(PermissionLookup.edit_remove, () => ({
      view: false,
      edit: true,
      remove: true,
    }))
    .with(PermissionLookup.view_edit_remove, () => ({
      view: true,
      edit: true,
      remove: true,
    }))
    .exhaustive()

function normalizeViewEditPermission(
  viewFieldName: string,
  editFieldName: string,
  permission: ViewEditPermission | undefined,
) {
  if (permission === undefined) {
    return {}
  }
  return {
    [viewFieldName]: permission === 'view:' || permission === 'view:edit' ? 't' : 'f',
    [editFieldName]: permission === ':edit' || permission === 'view:edit' ? 't' : 'f',
  }
}

export const PermissionLookup = {
  '': 0,
  view: 1,
  edit: 2,
  remove: 3,
  view_edit: 4,
  view_remove: 5,
  edit_remove: 6,
  view_edit_remove: 7,
} as const satisfies Record<string, number>

export type PermissionLookupKeyType = keyof typeof PermissionLookup
export type PermissionLookupValueOfType = ValueOf<typeof PermissionLookup>
/**
 * Playing it safe since we never know for sure when the BE decides to switch to a string/number
 */
export type PermissionLookupValueType =
  | (typeof PermissionLookup)[PermissionLookupKeyType]
  | `${(typeof PermissionLookup)[PermissionLookupKeyType]}`

export type FetchUsersReturnType = ReturnType<typeof parseUsersList>

export default {
  fetchUsers() {
    return apiCallerNoX('ct_fleet_get_user').then((res: FixMeAny) =>
      parseUsersList(res.ct_fleet_get_user || []),
    )
  },

  fetchUserFeatures(
    userId: string,
    metaData: {
      userSettings: Record<string, unknown>
      listGeofencesPermissionSetting: boolean
    },
  ) {
    return apiCallerNoX('ct_fleet_get_user_features_data', {
      client_user_id: userId,
    }).then((res: FixMeAny) => parseUserFeatures(res, metaData))
  },

  fetchUserAssignedVehicles(userId: string) {
    return apiCaller('ct_fleet_client_user_vehicle', {
      data: userId,
    }).then((res: FixMeAny) =>
      parseUserAssignedVehicles(res.ct_fleet_client_user_vehicle),
    )
  },

  fetchUserInfo(userId: string) {
    return apiCaller('ct_fleet_client_user_info', {
      data: userId,
    }).then((res: FixMeAny) =>
      parseFetchUserInfoResponse(res.ct_fleet_client_user_info),
    )
  },

  fetchUserVehicleGroups(userId: string) {
    return apiCaller(
      'ct_fleet_get_client_user_to_vehicle_group',
      {
        client_user_id: userId,
      },
      { noX: true },
    ).then((res: FixMeAny) => parseFetchUserVehicleGroupsResponse(res))
  },

  fetchUserDriverGroups(userId: string) {
    return apiCallerNoX('ct_fleet_get_client_user_to_driver_group', {
      client_user_id: userId,
    }).then((res: FixMeAny) => parseFetchUserDriverGroupsResponse(res))
  },

  fetchUserDrivers(userId: string) {
    return apiCaller(
      'ct_fleet_get_client_user_driver_permission',
      {
        client_user_id: userId,
      },
      { noX: true },
    ).then((res: FixMeAny) => parseFetchUserDriversResponse(res))
  },

  fetchUserVehicleGroupAccess(userId: string) {
    return apiCaller(
      'ct_fleet_get_client_user_vehicle_group_access',
      {
        client_user_id: userId,
      },
      { noX: true },
    ).then((res: FixMeAny) => parseFetchUserVehicleGroupAccessResponse(res))
  },

  fetchUserDriverGroupAccess(userId: string) {
    return apiCaller(
      'ct_fleet_get_client_user_driver_group_access',
      {
        client_user_id: userId,
      },
      { noX: true },
    ).then((res: FixMeAny) => parseFetchUserDriverGroupAccessResponse(res))
  },

  fetchClientPermissions(userId: string) {
    return apiCallerNoX('ct_fleet_get_client_data_access_permissions', {
      client_user_id: userId,
    }).then((res: FixMeAny) => parseFetchClientPermissionResponse(res))
  },

  updateUserVehicleGroups(
    userId: string,
    groups: Array<UpdateUserVehicleGroups.Group>,
  ) {
    return apiCaller(
      'ct_fleet_update_client_user_to_vehicle_group',
      {
        client_user_id: userId,
        permissions: groups.map((group) => ({
          group_vehicle_id: group.groupId,
          permission_id: normalizePermissions(group.permissions),
        })),
      },
      { noX: true },
    )
  },

  updateUserVehicleGroupAccess(
    userId: string,
    groupPermissions: UpdateUserVehicleGroupAccess.GroupPermissions,
  ) {
    return apiCaller(
      'ct_fleet_update_client_user_vehicle_group_access',
      {
        client_user_id: userId,
        group_permissions: groupPermissions,
      },
      { noX: true },
    )
  },

  updateUserDrivers(userId: string, drivers: Array<UpdateUserDrivers.Driver>) {
    return apiCaller(
      'ct_fleet_update_client_user_driver_permission',
      {
        client_user_id: userId,
        permissions: drivers.map((driver) => ({
          driver_id: driver.id,
          permission_id: normalizePermissions(driver.permissions),
        })),
      },
      { noX: true },
    )
  },

  updateUserDriverGroupAccess(
    userId: string,
    groupPermissions: UpdateUserDriverGroupAccess.GroupPermissions,
  ) {
    return apiCallerNoX('ct_fleet_update_client_user_driver_group_access', {
      client_user_id: userId,
      group_permissions: groupPermissions,
    })
  },

  updateUserClientPermissions(
    userId: string,
    permissions: Partial<
      Pick<UserPermissionsDataType, 'geofencePermission' | 'poiPermission'>
    >,
  ) {
    const apiParams: Ct_fleet_update_client_data_access_permissions.ApiParams = {
      client_user_id: userId,
      permissions: R.toPairs(permissions).reduce<
        Ct_fleet_update_client_data_access_permissions.ApiParams['permissions']
      >((acc, [key_, value]) => {
        const permissionKey = key_ as keyof typeof permissions

        const apiKey = match(permissionKey)
          .with('poiPermission', () => 'poi' as const)
          .with('geofencePermission', () => 'geofences' as const)
          .exhaustive()

        acc[apiKey] = {
          permission_id: normalizeUserPermission(value),
        }

        return acc
      }, {}),
    }

    return apiCallerNoX('ct_fleet_update_client_data_access_permissions', apiParams)
  },

  updateUserAdminPermissions(
    userId: string,
    permissions: Partial<
      Pick<
        UserPermissionsDataType,
        | 'adminDriverPermission'
        | 'adminDriverGroupsPermission'
        | 'adminPoiPermission'
        | 'adminGeofencePermission'
      >
    >,
  ) {
    const apiParams: Ct_fleet_update_admin_data_access_permissions.ApiParams = {
      client_user_id: userId,
      permissions: R.toPairs(permissions).reduce<
        Ct_fleet_update_admin_data_access_permissions.ApiParams['permissions']
      >((acc, [key_, value]) => {
        const permissionKey = key_ as keyof typeof permissions
        const apiKey = match(permissionKey)
          .with('adminPoiPermission', () => 'poi' as const)
          .with('adminGeofencePermission', () => 'geofences' as const)
          .with('adminDriverPermission', () => 'drivers' as const)
          .with('adminDriverGroupsPermission', () => 'driver_groups' as const)
          .exhaustive()

        acc[apiKey] = {
          permission_id: normalizeUserPermission(value),
        }

        return acc
      }, {}),
    }

    return apiCallerNoX('ct_fleet_update_admin_data_access_permissions', apiParams)
  },

  toggleUserLockStatus(userId: string, isLocked: boolean) {
    return apiCaller(
      'ct_fleet_client_user_lock',
      {
        data: {
          client_user_id: userId,
          is_locked: isLocked,
        },
      },
      { noX: true },
    )
  },

  updateUser(
    userId: string,
    {
      features,
      profile,
      vehicles,
    }: {
      profile?: UserProfileSchema
      features?: FetchUserInfo.UserBaseFeatures
      vehicles?: UpdateUserInfo.VehicleAssigned | true | null
    },
  ) {
    function normalizeFeatures(info?: FetchUserInfo.UserBaseFeatures) {
      return {
        base_features: info
          ? {
              ...normalizeViewEditPermission(
                'allow_subuser_tosee_mainuser_geofences',
                'allow_subuser_toedit_mainuser_geofences',
                info.subUserGeofenceSharing,
              ),
              ...normalizeViewEditPermission(
                'allow_mainuser_tosee_subuser_geofences',
                'allow_mainuser_toedit_subuser_geofences',
                info.mainUserGeofenceSharing,
              ),
              ...normalizeViewEditPermission(
                'allow_mainuser_tosee_subuser_geofences',
                'allow_mainuser_toedit_subuser_geofences',
                info.mainUserGeofenceSharing,
              ),
              ...normalizeViewEditPermission(
                'allow_subuser_tosee_mainuser_poi',
                'allow_subuser_toedit_mainuser_poi',
                info.subUserPOISharing,
              ),
              ...normalizeViewEditPermission(
                'allow_mainuser_tosee_subuser_poi',
                'allow_mainuser_toedit_subuser_poi',
                info.mainUserPOISharing,
              ),
            }
          : null,
      }
    }

    function normalizeProfile(info: UserProfileSchema | undefined): {
      general: Record<string, any> | null
      profile: Record<string, any> | null
    } {
      if (info === undefined) {
        return { general: null, profile: null }
      }

      return {
        general: {
          email_address: info.email,
          user_name: info.username,
          cell_no: info.cellPhone,
          department_ids: info.departmentIds,
          client_user_role_id: info.clientUserRoleId,
        },
        profile: getNormalizedProfileValuesIfIsWithCostsSchema(info),
      }
    }

    return apiCaller('ct_fleet_update_user', {
      client_user_id: userId,
      ...normalizeProfile(profile),
      ...normalizeFeatures(features),
      vehicles: vehicles ? vehicles : null,
    }).then((res: UpdateSubUser.ApiOutput) => res)
  },

  resetUserPassword(userId: string) {
    return apiCaller('ct_fleet_user_reset_password', {
      client_user_id: userId,
    })
  },
}
