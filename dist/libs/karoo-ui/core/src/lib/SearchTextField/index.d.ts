import { type ComponentProps, type MouseEventHandler } from 'react';
import SearchIcon from '@mui/icons-material/Search';
import type { Except } from 'type-fest';
import { type OutlinedTextFieldProps, type StandardTextFieldProps } from '../TextField';
export type SearchTextFieldProps = Pick<Except<StandardTextFieldProps | OutlinedTextFieldProps, 'onChange'>, 'placeholder' | 'size' | 'label' | 'variant' | 'fullWidth' | 'sx' | 'autoFocus' | 'onClick'> & {
    value: string;
    searchIconProps?: ComponentProps<typeof SearchIcon>;
    onClearIconClick: MouseEventHandler<HTMLButtonElement>;
    onChange: React.ChangeEventHandler<HTMLTextAreaElement | HTMLInputElement>;
};
/**
 * Wrapped with forwardRef to work with ClickAwayListener if needed
 */
export declare const SearchTextField: import("react").ForwardRefExoticComponent<Pick<Except<OutlinedTextFieldProps | StandardTextFieldProps, "onChange">, "sx" | "label" | "autoFocus" | "onClick" | "fullWidth" | "size" | "variant" | "placeholder"> & {
    value: string;
    searchIconProps?: ComponentProps<typeof SearchIcon>;
    onClearIconClick: MouseEventHandler<HTMLButtonElement>;
    onChange: React.ChangeEventHandler<HTMLTextAreaElement | HTMLInputElement>;
} & import("react").RefAttributes<import("react").Ref<any> | undefined>>;
export declare function useSearchTextField(initialValue: SearchTextFieldProps['value']): Pick<SearchTextFieldProps, 'onChange' | 'value' | 'onClearIconClick'>;
