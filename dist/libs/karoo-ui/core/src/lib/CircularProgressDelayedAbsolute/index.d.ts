import type { Except } from 'type-fest';
import { type CircularProgressDelayedProps } from '../CircularProgressDelayed';
export type CircularProgressDelayedAbsoluteProps = Except<CircularProgressDelayedProps, 'sx' | 'className' | 'classes'>;
/**
 * Only use this when you want to show a loading indicator in the center of a container.
 *
 * If you need to style CircularProgressDelayed (e.g - using 'className' or 'sx' props), please use __```CircularProgressDelayed``` directly__ instead
 */
export declare const CircularProgressDelayedAbsolute: (props: {
    delayedProps?: {
        loading?: boolean;
        delayInMs?: number;
    } | undefined;
    color?: import("@mui/types").OverridableStringUnion<"primary" | "secondary" | "error" | "info" | "success" | "warning" | "inherit", import("@mui/material").CircularProgressPropsColorOverrides> | undefined;
    disableShrink?: boolean | undefined;
    size?: number | string | undefined;
    thickness?: number | undefined;
    value?: number | undefined;
    variant?: "determinate" | "indeterminate" | undefined;
    style?: import("react").CSSProperties | undefined;
    tabIndex?: number | undefined | undefined;
    slot?: string | undefined | undefined;
    title?: string | undefined | undefined;
    defaultChecked?: boolean | undefined | undefined;
    defaultValue?: string | number | readonly string[] | undefined;
    suppressContentEditableWarning?: boolean | undefined | undefined;
    suppressHydrationWarning?: boolean | undefined | undefined;
    accessKey?: string | undefined | undefined;
    autoFocus?: boolean | undefined | undefined;
    contentEditable?: "inherit" | (boolean | "true" | "false") | "plaintext-only" | undefined;
    contextMenu?: string | undefined | undefined;
    dir?: string | undefined | undefined;
    draggable?: (boolean | "true" | "false") | undefined;
    hidden?: boolean | undefined | undefined;
    id?: string | undefined | undefined;
    lang?: string | undefined | undefined;
    nonce?: string | undefined | undefined;
    spellCheck?: (boolean | "true" | "false") | undefined;
    translate?: "yes" | "no" | undefined | undefined;
    radioGroup?: string | undefined | undefined;
    role?: import("react").AriaRole | undefined;
    about?: string | undefined | undefined;
    content?: string | undefined | undefined;
    datatype?: string | undefined | undefined;
    inlist?: any;
    prefix?: string | undefined | undefined;
    property?: string | undefined | undefined;
    rel?: string | undefined | undefined;
    resource?: string | undefined | undefined;
    rev?: string | undefined | undefined;
    typeof?: string | undefined | undefined;
    vocab?: string | undefined | undefined;
    autoCapitalize?: string | undefined | undefined;
    autoCorrect?: string | undefined | undefined;
    autoSave?: string | undefined | undefined;
    itemProp?: string | undefined | undefined;
    itemScope?: boolean | undefined | undefined;
    itemType?: string | undefined | undefined;
    itemID?: string | undefined | undefined;
    itemRef?: string | undefined | undefined;
    results?: number | undefined | undefined;
    security?: string | undefined | undefined;
    unselectable?: "on" | "off" | undefined | undefined;
    inputMode?: "none" | "text" | "tel" | "url" | "email" | "numeric" | "decimal" | "search" | undefined | undefined;
    is?: string | undefined | undefined;
    "aria-activedescendant"?: string | undefined | undefined;
    "aria-atomic"?: (boolean | "true" | "false") | undefined;
    "aria-autocomplete"?: "none" | "inline" | "list" | "both" | undefined | undefined;
    "aria-braillelabel"?: string | undefined | undefined;
    "aria-brailleroledescription"?: string | undefined | undefined;
    "aria-busy"?: (boolean | "true" | "false") | undefined;
    "aria-checked"?: boolean | "false" | "mixed" | "true" | undefined | undefined;
    "aria-colcount"?: number | undefined | undefined;
    "aria-colindex"?: number | undefined | undefined;
    "aria-colindextext"?: string | undefined | undefined;
    "aria-colspan"?: number | undefined | undefined;
    "aria-controls"?: string | undefined | undefined;
    "aria-current"?: boolean | "false" | "true" | "page" | "step" | "location" | "date" | "time" | undefined | undefined;
    "aria-describedby"?: string | undefined | undefined;
    "aria-description"?: string | undefined | undefined;
    "aria-details"?: string | undefined | undefined;
    "aria-disabled"?: (boolean | "true" | "false") | undefined;
    "aria-dropeffect"?: "none" | "copy" | "execute" | "link" | "move" | "popup" | undefined | undefined;
    "aria-errormessage"?: string | undefined | undefined;
    "aria-expanded"?: (boolean | "true" | "false") | undefined;
    "aria-flowto"?: string | undefined | undefined;
    "aria-grabbed"?: (boolean | "true" | "false") | undefined;
    "aria-haspopup"?: boolean | "false" | "true" | "menu" | "listbox" | "tree" | "grid" | "dialog" | undefined | undefined;
    "aria-hidden"?: (boolean | "true" | "false") | undefined;
    "aria-invalid"?: boolean | "false" | "true" | "grammar" | "spelling" | undefined | undefined;
    "aria-keyshortcuts"?: string | undefined | undefined;
    "aria-label"?: string | undefined | undefined;
    "aria-labelledby"?: string | undefined | undefined;
    "aria-level"?: number | undefined | undefined;
    "aria-live"?: "off" | "assertive" | "polite" | undefined | undefined;
    "aria-modal"?: (boolean | "true" | "false") | undefined;
    "aria-multiline"?: (boolean | "true" | "false") | undefined;
    "aria-multiselectable"?: (boolean | "true" | "false") | undefined;
    "aria-orientation"?: "horizontal" | "vertical" | undefined | undefined;
    "aria-owns"?: string | undefined | undefined;
    "aria-placeholder"?: string | undefined | undefined;
    "aria-posinset"?: number | undefined | undefined;
    "aria-pressed"?: boolean | "false" | "mixed" | "true" | undefined | undefined;
    "aria-readonly"?: (boolean | "true" | "false") | undefined;
    "aria-relevant"?: "additions" | "additions removals" | "additions text" | "all" | "removals" | "removals additions" | "removals text" | "text" | "text additions" | "text removals" | undefined | undefined;
    "aria-required"?: (boolean | "true" | "false") | undefined;
    "aria-roledescription"?: string | undefined | undefined;
    "aria-rowcount"?: number | undefined | undefined;
    "aria-rowindex"?: number | undefined | undefined;
    "aria-rowindextext"?: string | undefined | undefined;
    "aria-rowspan"?: number | undefined | undefined;
    "aria-selected"?: (boolean | "true" | "false") | undefined;
    "aria-setsize"?: number | undefined | undefined;
    "aria-sort"?: "none" | "ascending" | "descending" | "other" | undefined | undefined;
    "aria-valuemax"?: number | undefined | undefined;
    "aria-valuemin"?: number | undefined | undefined;
    "aria-valuenow"?: number | undefined | undefined;
    "aria-valuetext"?: string | undefined | undefined;
    dangerouslySetInnerHTML?: {
        __html: string | TrustedHTML;
    } | undefined | undefined;
    onCopy?: import("react").ClipboardEventHandler<HTMLSpanElement> | undefined;
    onCopyCapture?: import("react").ClipboardEventHandler<HTMLSpanElement> | undefined;
    onCut?: import("react").ClipboardEventHandler<HTMLSpanElement> | undefined;
    onCutCapture?: import("react").ClipboardEventHandler<HTMLSpanElement> | undefined;
    onPaste?: import("react").ClipboardEventHandler<HTMLSpanElement> | undefined;
    onPasteCapture?: import("react").ClipboardEventHandler<HTMLSpanElement> | undefined;
    onCompositionEnd?: import("react").CompositionEventHandler<HTMLSpanElement> | undefined;
    onCompositionEndCapture?: import("react").CompositionEventHandler<HTMLSpanElement> | undefined;
    onCompositionStart?: import("react").CompositionEventHandler<HTMLSpanElement> | undefined;
    onCompositionStartCapture?: import("react").CompositionEventHandler<HTMLSpanElement> | undefined;
    onCompositionUpdate?: import("react").CompositionEventHandler<HTMLSpanElement> | undefined;
    onCompositionUpdateCapture?: import("react").CompositionEventHandler<HTMLSpanElement> | undefined;
    onFocus?: import("react").FocusEventHandler<HTMLSpanElement> | undefined;
    onFocusCapture?: import("react").FocusEventHandler<HTMLSpanElement> | undefined;
    onBlur?: import("react").FocusEventHandler<HTMLSpanElement> | undefined;
    onBlurCapture?: import("react").FocusEventHandler<HTMLSpanElement> | undefined;
    onChange?: import("react").FormEventHandler<HTMLSpanElement> | undefined;
    onChangeCapture?: import("react").FormEventHandler<HTMLSpanElement> | undefined;
    onBeforeInput?: import("react").FormEventHandler<HTMLSpanElement> | undefined;
    onBeforeInputCapture?: import("react").FormEventHandler<HTMLSpanElement> | undefined;
    onInput?: import("react").FormEventHandler<HTMLSpanElement> | undefined;
    onInputCapture?: import("react").FormEventHandler<HTMLSpanElement> | undefined;
    onReset?: import("react").FormEventHandler<HTMLSpanElement> | undefined;
    onResetCapture?: import("react").FormEventHandler<HTMLSpanElement> | undefined;
    onSubmit?: import("react").FormEventHandler<HTMLSpanElement> | undefined;
    onSubmitCapture?: import("react").FormEventHandler<HTMLSpanElement> | undefined;
    onInvalid?: import("react").FormEventHandler<HTMLSpanElement> | undefined;
    onInvalidCapture?: import("react").FormEventHandler<HTMLSpanElement> | undefined;
    onLoad?: import("react").ReactEventHandler<HTMLSpanElement> | undefined;
    onLoadCapture?: import("react").ReactEventHandler<HTMLSpanElement> | undefined;
    onError?: import("react").ReactEventHandler<HTMLSpanElement> | undefined;
    onErrorCapture?: import("react").ReactEventHandler<HTMLSpanElement> | undefined;
    onKeyDown?: import("react").KeyboardEventHandler<HTMLSpanElement> | undefined;
    onKeyDownCapture?: import("react").KeyboardEventHandler<HTMLSpanElement> | undefined;
    onKeyPress?: import("react").KeyboardEventHandler<HTMLSpanElement> | undefined;
    onKeyPressCapture?: import("react").KeyboardEventHandler<HTMLSpanElement> | undefined;
    onKeyUp?: import("react").KeyboardEventHandler<HTMLSpanElement> | undefined;
    onKeyUpCapture?: import("react").KeyboardEventHandler<HTMLSpanElement> | undefined;
    onAbort?: import("react").ReactEventHandler<HTMLSpanElement> | undefined;
    onAbortCapture?: import("react").ReactEventHandler<HTMLSpanElement> | undefined;
    onCanPlay?: import("react").ReactEventHandler<HTMLSpanElement> | undefined;
    onCanPlayCapture?: import("react").ReactEventHandler<HTMLSpanElement> | undefined;
    onCanPlayThrough?: import("react").ReactEventHandler<HTMLSpanElement> | undefined;
    onCanPlayThroughCapture?: import("react").ReactEventHandler<HTMLSpanElement> | undefined;
    onDurationChange?: import("react").ReactEventHandler<HTMLSpanElement> | undefined;
    onDurationChangeCapture?: import("react").ReactEventHandler<HTMLSpanElement> | undefined;
    onEmptied?: import("react").ReactEventHandler<HTMLSpanElement> | undefined;
    onEmptiedCapture?: import("react").ReactEventHandler<HTMLSpanElement> | undefined;
    onEncrypted?: import("react").ReactEventHandler<HTMLSpanElement> | undefined;
    onEncryptedCapture?: import("react").ReactEventHandler<HTMLSpanElement> | undefined;
    onEnded?: import("react").ReactEventHandler<HTMLSpanElement> | undefined;
    onEndedCapture?: import("react").ReactEventHandler<HTMLSpanElement> | undefined;
    onLoadedData?: import("react").ReactEventHandler<HTMLSpanElement> | undefined;
    onLoadedDataCapture?: import("react").ReactEventHandler<HTMLSpanElement> | undefined;
    onLoadedMetadata?: import("react").ReactEventHandler<HTMLSpanElement> | undefined;
    onLoadedMetadataCapture?: import("react").ReactEventHandler<HTMLSpanElement> | undefined;
    onLoadStart?: import("react").ReactEventHandler<HTMLSpanElement> | undefined;
    onLoadStartCapture?: import("react").ReactEventHandler<HTMLSpanElement> | undefined;
    onPause?: import("react").ReactEventHandler<HTMLSpanElement> | undefined;
    onPauseCapture?: import("react").ReactEventHandler<HTMLSpanElement> | undefined;
    onPlay?: import("react").ReactEventHandler<HTMLSpanElement> | undefined;
    onPlayCapture?: import("react").ReactEventHandler<HTMLSpanElement> | undefined;
    onPlaying?: import("react").ReactEventHandler<HTMLSpanElement> | undefined;
    onPlayingCapture?: import("react").ReactEventHandler<HTMLSpanElement> | undefined;
    onProgress?: import("react").ReactEventHandler<HTMLSpanElement> | undefined;
    onProgressCapture?: import("react").ReactEventHandler<HTMLSpanElement> | undefined;
    onRateChange?: import("react").ReactEventHandler<HTMLSpanElement> | undefined;
    onRateChangeCapture?: import("react").ReactEventHandler<HTMLSpanElement> | undefined;
    onResize?: import("react").ReactEventHandler<HTMLSpanElement> | undefined;
    onResizeCapture?: import("react").ReactEventHandler<HTMLSpanElement> | undefined;
    onSeeked?: import("react").ReactEventHandler<HTMLSpanElement> | undefined;
    onSeekedCapture?: import("react").ReactEventHandler<HTMLSpanElement> | undefined;
    onSeeking?: import("react").ReactEventHandler<HTMLSpanElement> | undefined;
    onSeekingCapture?: import("react").ReactEventHandler<HTMLSpanElement> | undefined;
    onStalled?: import("react").ReactEventHandler<HTMLSpanElement> | undefined;
    onStalledCapture?: import("react").ReactEventHandler<HTMLSpanElement> | undefined;
    onSuspend?: import("react").ReactEventHandler<HTMLSpanElement> | undefined;
    onSuspendCapture?: import("react").ReactEventHandler<HTMLSpanElement> | undefined;
    onTimeUpdate?: import("react").ReactEventHandler<HTMLSpanElement> | undefined;
    onTimeUpdateCapture?: import("react").ReactEventHandler<HTMLSpanElement> | undefined;
    onVolumeChange?: import("react").ReactEventHandler<HTMLSpanElement> | undefined;
    onVolumeChangeCapture?: import("react").ReactEventHandler<HTMLSpanElement> | undefined;
    onWaiting?: import("react").ReactEventHandler<HTMLSpanElement> | undefined;
    onWaitingCapture?: import("react").ReactEventHandler<HTMLSpanElement> | undefined;
    onAuxClick?: import("react").MouseEventHandler<HTMLSpanElement> | undefined;
    onAuxClickCapture?: import("react").MouseEventHandler<HTMLSpanElement> | undefined;
    onClick?: import("react").MouseEventHandler<HTMLSpanElement> | undefined;
    onClickCapture?: import("react").MouseEventHandler<HTMLSpanElement> | undefined;
    onContextMenu?: import("react").MouseEventHandler<HTMLSpanElement> | undefined;
    onContextMenuCapture?: import("react").MouseEventHandler<HTMLSpanElement> | undefined;
    onDoubleClick?: import("react").MouseEventHandler<HTMLSpanElement> | undefined;
    onDoubleClickCapture?: import("react").MouseEventHandler<HTMLSpanElement> | undefined;
    onDrag?: import("react").DragEventHandler<HTMLSpanElement> | undefined;
    onDragCapture?: import("react").DragEventHandler<HTMLSpanElement> | undefined;
    onDragEnd?: import("react").DragEventHandler<HTMLSpanElement> | undefined;
    onDragEndCapture?: import("react").DragEventHandler<HTMLSpanElement> | undefined;
    onDragEnter?: import("react").DragEventHandler<HTMLSpanElement> | undefined;
    onDragEnterCapture?: import("react").DragEventHandler<HTMLSpanElement> | undefined;
    onDragExit?: import("react").DragEventHandler<HTMLSpanElement> | undefined;
    onDragExitCapture?: import("react").DragEventHandler<HTMLSpanElement> | undefined;
    onDragLeave?: import("react").DragEventHandler<HTMLSpanElement> | undefined;
    onDragLeaveCapture?: import("react").DragEventHandler<HTMLSpanElement> | undefined;
    onDragOver?: import("react").DragEventHandler<HTMLSpanElement> | undefined;
    onDragOverCapture?: import("react").DragEventHandler<HTMLSpanElement> | undefined;
    onDragStart?: import("react").DragEventHandler<HTMLSpanElement> | undefined;
    onDragStartCapture?: import("react").DragEventHandler<HTMLSpanElement> | undefined;
    onDrop?: import("react").DragEventHandler<HTMLSpanElement> | undefined;
    onDropCapture?: import("react").DragEventHandler<HTMLSpanElement> | undefined;
    onMouseDown?: import("react").MouseEventHandler<HTMLSpanElement> | undefined;
    onMouseDownCapture?: import("react").MouseEventHandler<HTMLSpanElement> | undefined;
    onMouseEnter?: import("react").MouseEventHandler<HTMLSpanElement> | undefined;
    onMouseLeave?: import("react").MouseEventHandler<HTMLSpanElement> | undefined;
    onMouseMove?: import("react").MouseEventHandler<HTMLSpanElement> | undefined;
    onMouseMoveCapture?: import("react").MouseEventHandler<HTMLSpanElement> | undefined;
    onMouseOut?: import("react").MouseEventHandler<HTMLSpanElement> | undefined;
    onMouseOutCapture?: import("react").MouseEventHandler<HTMLSpanElement> | undefined;
    onMouseOver?: import("react").MouseEventHandler<HTMLSpanElement> | undefined;
    onMouseOverCapture?: import("react").MouseEventHandler<HTMLSpanElement> | undefined;
    onMouseUp?: import("react").MouseEventHandler<HTMLSpanElement> | undefined;
    onMouseUpCapture?: import("react").MouseEventHandler<HTMLSpanElement> | undefined;
    onSelect?: import("react").ReactEventHandler<HTMLSpanElement> | undefined;
    onSelectCapture?: import("react").ReactEventHandler<HTMLSpanElement> | undefined;
    onTouchCancel?: import("react").TouchEventHandler<HTMLSpanElement> | undefined;
    onTouchCancelCapture?: import("react").TouchEventHandler<HTMLSpanElement> | undefined;
    onTouchEnd?: import("react").TouchEventHandler<HTMLSpanElement> | undefined;
    onTouchEndCapture?: import("react").TouchEventHandler<HTMLSpanElement> | undefined;
    onTouchMove?: import("react").TouchEventHandler<HTMLSpanElement> | undefined;
    onTouchMoveCapture?: import("react").TouchEventHandler<HTMLSpanElement> | undefined;
    onTouchStart?: import("react").TouchEventHandler<HTMLSpanElement> | undefined;
    onTouchStartCapture?: import("react").TouchEventHandler<HTMLSpanElement> | undefined;
    onPointerDown?: import("react").PointerEventHandler<HTMLSpanElement> | undefined;
    onPointerDownCapture?: import("react").PointerEventHandler<HTMLSpanElement> | undefined;
    onPointerMove?: import("react").PointerEventHandler<HTMLSpanElement> | undefined;
    onPointerMoveCapture?: import("react").PointerEventHandler<HTMLSpanElement> | undefined;
    onPointerUp?: import("react").PointerEventHandler<HTMLSpanElement> | undefined;
    onPointerUpCapture?: import("react").PointerEventHandler<HTMLSpanElement> | undefined;
    onPointerCancel?: import("react").PointerEventHandler<HTMLSpanElement> | undefined;
    onPointerCancelCapture?: import("react").PointerEventHandler<HTMLSpanElement> | undefined;
    onPointerEnter?: import("react").PointerEventHandler<HTMLSpanElement> | undefined;
    onPointerLeave?: import("react").PointerEventHandler<HTMLSpanElement> | undefined;
    onPointerOver?: import("react").PointerEventHandler<HTMLSpanElement> | undefined;
    onPointerOverCapture?: import("react").PointerEventHandler<HTMLSpanElement> | undefined;
    onPointerOut?: import("react").PointerEventHandler<HTMLSpanElement> | undefined;
    onPointerOutCapture?: import("react").PointerEventHandler<HTMLSpanElement> | undefined;
    onGotPointerCapture?: import("react").PointerEventHandler<HTMLSpanElement> | undefined;
    onGotPointerCaptureCapture?: import("react").PointerEventHandler<HTMLSpanElement> | undefined;
    onLostPointerCapture?: import("react").PointerEventHandler<HTMLSpanElement> | undefined;
    onLostPointerCaptureCapture?: import("react").PointerEventHandler<HTMLSpanElement> | undefined;
    onScroll?: import("react").UIEventHandler<HTMLSpanElement> | undefined;
    onScrollCapture?: import("react").UIEventHandler<HTMLSpanElement> | undefined;
    onWheel?: import("react").WheelEventHandler<HTMLSpanElement> | undefined;
    onWheelCapture?: import("react").WheelEventHandler<HTMLSpanElement> | undefined;
    onAnimationStart?: import("react").AnimationEventHandler<HTMLSpanElement> | undefined;
    onAnimationStartCapture?: import("react").AnimationEventHandler<HTMLSpanElement> | undefined;
    onAnimationEnd?: import("react").AnimationEventHandler<HTMLSpanElement> | undefined;
    onAnimationEndCapture?: import("react").AnimationEventHandler<HTMLSpanElement> | undefined;
    onAnimationIteration?: import("react").AnimationEventHandler<HTMLSpanElement> | undefined;
    onAnimationIterationCapture?: import("react").AnimationEventHandler<HTMLSpanElement> | undefined;
    onTransitionEnd?: import("react").TransitionEventHandler<HTMLSpanElement> | undefined;
    onTransitionEndCapture?: import("react").TransitionEventHandler<HTMLSpanElement> | undefined;
    ref?: import("react").Ref<unknown> | undefined;
} & import("react").RefAttributes<unknown>) => React.ReactElement | null;
