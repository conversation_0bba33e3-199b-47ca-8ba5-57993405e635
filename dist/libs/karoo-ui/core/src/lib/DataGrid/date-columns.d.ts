import { type GridColDef, type GridColType, type GridFilterInputValueProps, type GridFilterItem, type GridFilterOperator, type GridRenderEditCellParams, type GridValidRowModel } from '@mui/x-data-grid-premium';
import type { GridApiCommunity } from '@mui/x-data-grid/models/api/gridApiCommunity';
import type { Except } from 'type-fest';
import { type KarooExtendedLocalesContextValue } from '../KarooExtendedLocalesContext';
import { usePickersAdapterContextUtils } from '../usePickersAdapterContextUtils';
import type { GridBaseColDef } from './types-overrides-public';
type PickersAdapterUtils = ReturnType<typeof usePickersAdapterContextUtils>;
declare function getGridDateColumnFilterOperators({ showTime, pickersAdapterUtils, filterMode, extendedLocales, }: {
    showTime: boolean;
    pickersAdapterUtils: PickersAdapterUtils;
    filterMode: 'server' | 'client';
    extendedLocales: KarooExtendedLocalesContextValue;
}): readonly [{
    value: "range";
    label: string;
    headerLabel: string;
    getApplyFilterFn: (filterItem_: GridFilterItem) => ((value: Date | null | undefined) => boolean) | null;
    InputComponent: typeof GridFilterDateRangeInput;
    InputComponentProps: {};
}, {
    readonly value: "is";
    readonly getApplyFilterFn: (filterItem: GridFilterItem) => ((value: Date | null | undefined, row: GridValidRowModel, column: GridColDef<GridValidRowModel, Date | null | undefined, unknown>, apiRef: React.MutableRefObject<GridApiCommunity>) => boolean) | null;
    readonly InputComponent: typeof GridFilterDateInput;
    readonly InputComponentProps: {
        showTime: boolean;
        filterMode: "server" | "client";
    };
}, {
    readonly value: "not";
    readonly getApplyFilterFn: (filterItem: GridFilterItem) => ((value: Date | null | undefined, row: GridValidRowModel, column: GridColDef<GridValidRowModel, Date | null | undefined, unknown>, apiRef: React.MutableRefObject<GridApiCommunity>) => boolean) | null;
    readonly InputComponent: typeof GridFilterDateInput;
    readonly InputComponentProps: {
        showTime: boolean;
        filterMode: "server" | "client";
    };
}, {
    readonly value: "after";
    readonly getApplyFilterFn: (filterItem: GridFilterItem) => ((value: Date | null | undefined, row: GridValidRowModel, column: GridColDef<GridValidRowModel, Date | null | undefined, unknown>, apiRef: React.MutableRefObject<GridApiCommunity>) => boolean) | null;
    readonly InputComponent: typeof GridFilterDateInput;
    readonly InputComponentProps: {
        showTime: boolean;
        filterMode: "server" | "client";
    };
}, {
    readonly value: "onOrAfter";
    readonly getApplyFilterFn: (filterItem: GridFilterItem) => ((value: Date | null | undefined, row: GridValidRowModel, column: GridColDef<GridValidRowModel, Date | null | undefined, unknown>, apiRef: React.MutableRefObject<GridApiCommunity>) => boolean) | null;
    readonly InputComponent: typeof GridFilterDateInput;
    readonly InputComponentProps: {
        showTime: boolean;
        filterMode: "server" | "client";
    };
}, {
    readonly value: "before";
    readonly getApplyFilterFn: (filterItem: GridFilterItem) => ((value: Date | null | undefined, row: GridValidRowModel, column: GridColDef<GridValidRowModel, Date | null | undefined, unknown>, apiRef: React.MutableRefObject<GridApiCommunity>) => boolean) | null;
    readonly InputComponent: typeof GridFilterDateInput;
    readonly InputComponentProps: {
        showTime: boolean;
        filterMode: "server" | "client";
    };
}, {
    readonly value: "onOrBefore";
    readonly getApplyFilterFn: (filterItem: GridFilterItem) => ((value: Date | null | undefined, row: GridValidRowModel, column: GridColDef<GridValidRowModel, Date | null | undefined, unknown>, apiRef: React.MutableRefObject<GridApiCommunity>) => boolean) | null;
    readonly InputComponent: typeof GridFilterDateInput;
    readonly InputComponentProps: {
        showTime: boolean;
        filterMode: "server" | "client";
    };
}, {
    readonly value: "isEmpty";
    readonly getApplyFilterFn: () => (value: Date | null | undefined) => boolean;
    readonly requiresFilterValue: false;
}, {
    readonly value: "isNotEmpty";
    readonly getApplyFilterFn: () => (value: Date | null | undefined) => boolean;
    readonly requiresFilterValue: false;
}];
type GridDateColumnFilterOperator = ReturnType<typeof getGridDateColumnFilterOperators>[number]['value'];
type GridFilterDateInputProps = GridFilterInputValueProps & {
    showTime?: boolean;
    filterMode: 'server' | 'client';
};
declare function GridFilterDateInput({ item, showTime, applyValue, apiRef, filterMode, }: GridFilterDateInputProps): import("react/jsx-runtime").JSX.Element;
type GridFilterDateRangeInputProps = GridFilterInputValueProps;
declare function GridFilterDateRangeInput({ item, applyValue, apiRef, variant, size, }: GridFilterDateRangeInputProps): import("react/jsx-runtime").JSX.Element;
type DataGridDateColDef<R extends GridValidRowModel, V, F> = Except<GridBaseColDef<R, V, F>, 'valueFormatter' | 'type' | 'sortComparator' | 'align' | 'valueGetter'> & {
    valueGetter: (value: never, row: R, column: GridColDef<R, any, F>, apiRef: React.MutableRefObject<GridApiCommunity>) => V;
    valueFormatter?: (value: V, row: R, apiRef: React.MutableRefObject<GridApiCommunity>, extraInfo: {
        defaultFormatter: (date: Date) => string;
    }) => F;
};
type DataGridDateTimeColDef<R extends GridValidRowModel, V, F> = DataGridDateColDef<R, V, F>;
/**
 * @deprecated Use `columnHelper.date()` or `columnHelper.dateTime()` from `useDataGridColumnHelper` hook, instead.
 */
declare function useDataGridDateColumns({ filterMode }: {
    filterMode: 'server' | 'client';
}): {
    createDateTimeColumn: <R extends GridValidRowModel, V extends Date | null | undefined, F extends string | number | null>({ valueFormatter, renderCell, ...columnDefinition }: DataGridDateTimeColDef<R, V, F>) => {
        groupable: boolean;
        aggregable: boolean;
        display?: "text" | "flex";
        maxWidth?: number;
        minWidth?: number;
        width: number;
        flex?: number;
        colSpan?: number | import("@mui/x-data-grid-premium").GridColSpanFn<R, V, F> | import("@mui/x-data-grid-premium").GridColSpanFn<any, V, F> | undefined;
        disableColumnMenu?: boolean;
        sortingOrder?: readonly import("@mui/x-data-grid-premium").GridSortDirection[];
        description?: string;
        field: string;
        headerName?: string;
        hideable?: boolean;
        sortable?: boolean;
        resizable: boolean;
        editable?: boolean;
        pinnable?: boolean;
        getSortComparator?: ((sortDirection: import("@mui/x-data-grid-premium").GridSortDirection) => import("@mui/x-data-grid-premium").GridComparatorFn<V> | undefined) | undefined;
        valueSetter?: import("@mui/x-data-grid-premium").GridValueSetter<R, V, F> | import("@mui/x-data-grid-premium").GridValueSetter<any, V, F> | undefined;
        valueParser?: import("@mui/x-data-grid-premium").GridValueParser<R, V, F> | import("@mui/x-data-grid-premium").GridValueParser<any, V, F> | undefined;
        cellClassName?: string | import("@mui/x-data-grid-premium").GridCellClassFn<R, V> | undefined;
        renderEditCell: ((params: GridRenderEditCellParams<R, V, F, import("@mui/x-data-grid-premium").GridTreeNodeWithRender>) => React.ReactNode) | ((params: GridRenderEditCellParams<R, V, F, import("@mui/x-data-grid-premium").GridTreeNodeWithRender>) => import("react/jsx-runtime").JSX.Element);
        preProcessEditCellProps?: ((params: import("@mui/x-data-grid-premium").GridPreProcessEditCellProps) => import("@mui/x-data-grid-premium").GridEditCellProps | Promise<import("@mui/x-data-grid-premium").GridEditCellProps>) | undefined;
        headerClassName?: import("@mui/x-data-grid-premium").GridColumnHeaderClassNamePropType;
        renderHeader?: ((params: import("@mui/x-data-grid-premium").GridColumnHeaderParams<R, V, F>) => React.ReactNode) | ((params: import("@mui/x-data-grid-premium").GridColumnHeaderParams<any, V, F>) => React.ReactNode) | undefined;
        headerAlign?: import("@mui/x-data-grid-premium").GridAlignment;
        hideSortIcons?: boolean;
        filterable?: boolean;
        filterOperators: readonly GridFilterOperator<any, any, any>[] | readonly GridFilterOperator<R, V, F>[] | undefined;
        getApplyQuickFilterFn?: import("@mui/x-data-grid-premium").GetApplyQuickFilterFn<R, V> | import("@mui/x-data-grid-premium").GetApplyQuickFilterFn<any, V> | undefined;
        disableReorder?: boolean;
        disableExport?: boolean;
        renderHeaderFilter?: (params: import("@mui/x-data-grid-premium").GridRenderHeaderFilterProps) => React.ReactNode;
        availableAggregationFunctions?: string[];
        groupingValueGetter?: import("@mui/x-data-grid-premium").GridGroupingValueGetter<R> | import("@mui/x-data-grid-premium").GridGroupingValueGetter<any> | undefined;
        pastedValueParser?: import("@mui/x-data-grid-premium").GridPastedValueParser<R, V, F> | import("@mui/x-data-grid-premium").GridPastedValueParser<any, V, F> | undefined;
        valueGetter: (value: never, row: R, column: GridColDef<R, any, F>, apiRef: React.MutableRefObject<GridApiCommunity>) => V;
        type: GridColType;
        valueFormatter: (value: V, row: R, _column: GridColDef<R, V, F>, apiRef: import("react").MutableRefObject<GridApiCommunity>) => F;
        renderCell: ((params: import("@mui/x-data-grid-premium").GridRenderCellParams<R, V, F, import("@mui/x-data-grid-premium").GridTreeNodeWithRender>) => React.ReactNode) | (({ formattedValue }: import("@mui/x-data-grid-premium").GridRenderCellParams<R, V, F, import("@mui/x-data-grid-premium").GridTreeNodeWithRender>) => import("react/jsx-runtime").JSX.Element);
        align?: import("@mui/x-data-grid-premium").GridAlignment | undefined;
        sortComparator?: import("@mui/x-data-grid-premium").GridComparatorFn<V> | undefined;
    };
    createDateColumn: <R extends GridValidRowModel, V extends Date | null | undefined, F extends string | number | null>({ valueFormatter, renderCell, ...columnDefinition }: DataGridDateColDef<R, V, F>) => {
        groupable: boolean;
        aggregable: boolean;
        display?: "text" | "flex";
        maxWidth?: number;
        minWidth?: number;
        width?: number;
        flex?: number;
        colSpan?: number | import("@mui/x-data-grid-premium").GridColSpanFn<R, V, F> | import("@mui/x-data-grid-premium").GridColSpanFn<any, V, F> | undefined;
        disableColumnMenu?: boolean;
        sortingOrder?: readonly import("@mui/x-data-grid-premium").GridSortDirection[];
        description?: string;
        field: string;
        headerName?: string;
        hideable?: boolean;
        sortable?: boolean;
        resizable: boolean;
        editable?: boolean;
        pinnable?: boolean;
        getSortComparator?: ((sortDirection: import("@mui/x-data-grid-premium").GridSortDirection) => import("@mui/x-data-grid-premium").GridComparatorFn<V> | undefined) | undefined;
        valueSetter?: import("@mui/x-data-grid-premium").GridValueSetter<R, V, F> | import("@mui/x-data-grid-premium").GridValueSetter<any, V, F> | undefined;
        valueParser?: import("@mui/x-data-grid-premium").GridValueParser<R, V, F> | import("@mui/x-data-grid-premium").GridValueParser<any, V, F> | undefined;
        cellClassName?: string | import("@mui/x-data-grid-premium").GridCellClassFn<R, V> | undefined;
        renderEditCell: ((params: GridRenderEditCellParams<R, V, F, import("@mui/x-data-grid-premium").GridTreeNodeWithRender>) => React.ReactNode) | ((params: GridRenderEditCellParams<R, V, F, import("@mui/x-data-grid-premium").GridTreeNodeWithRender>) => import("react/jsx-runtime").JSX.Element);
        preProcessEditCellProps?: ((params: import("@mui/x-data-grid-premium").GridPreProcessEditCellProps) => import("@mui/x-data-grid-premium").GridEditCellProps | Promise<import("@mui/x-data-grid-premium").GridEditCellProps>) | undefined;
        headerClassName?: import("@mui/x-data-grid-premium").GridColumnHeaderClassNamePropType;
        renderHeader?: ((params: import("@mui/x-data-grid-premium").GridColumnHeaderParams<R, V, F>) => React.ReactNode) | ((params: import("@mui/x-data-grid-premium").GridColumnHeaderParams<any, V, F>) => React.ReactNode) | undefined;
        headerAlign?: import("@mui/x-data-grid-premium").GridAlignment;
        hideSortIcons?: boolean;
        filterable?: boolean;
        filterOperators: readonly GridFilterOperator<any, any, any>[] | readonly GridFilterOperator<R, V, F>[] | undefined;
        getApplyQuickFilterFn?: import("@mui/x-data-grid-premium").GetApplyQuickFilterFn<R, V> | import("@mui/x-data-grid-premium").GetApplyQuickFilterFn<any, V> | undefined;
        disableReorder?: boolean;
        disableExport?: boolean;
        renderHeaderFilter?: (params: import("@mui/x-data-grid-premium").GridRenderHeaderFilterProps) => React.ReactNode;
        availableAggregationFunctions?: string[];
        groupingValueGetter?: import("@mui/x-data-grid-premium").GridGroupingValueGetter<any> | import("@mui/x-data-grid-premium").GridGroupingValueGetter<R> | undefined;
        pastedValueParser?: import("@mui/x-data-grid-premium").GridPastedValueParser<R, V, F> | import("@mui/x-data-grid-premium").GridPastedValueParser<any, V, F> | undefined;
        valueGetter: (value: never, row: R, column: GridColDef<R, any, F>, apiRef: React.MutableRefObject<GridApiCommunity>) => V;
        type: GridColType;
        valueFormatter: (value: V, row: R, _column: GridColDef<R, V, F>, apiRef: import("react").MutableRefObject<GridApiCommunity>) => F;
        renderCell: ((params: import("@mui/x-data-grid-premium").GridRenderCellParams<R, V, F, import("@mui/x-data-grid-premium").GridTreeNodeWithRender>) => React.ReactNode) | (({ formattedValue }: import("@mui/x-data-grid-premium").GridRenderCellParams<R, V, F, import("@mui/x-data-grid-premium").GridTreeNodeWithRender>) => import("react/jsx-runtime").JSX.Element);
        align?: import("@mui/x-data-grid-premium").GridAlignment | undefined;
        sortComparator?: import("@mui/x-data-grid-premium").GridComparatorFn<V> | undefined;
    };
    dateTimeColDefaultFormatter: (dateValue: Date) => string;
    dateColDefaultFormatter: (dateValue: Date) => string;
    getGridDateColumnOperators: ({ showTime }: {
        showTime: boolean;
    }) => readonly [{
        value: "range";
        label: string;
        headerLabel: string;
        getApplyFilterFn: (filterItem_: GridFilterItem) => ((value: Date | null | undefined) => boolean) | null;
        InputComponent: typeof GridFilterDateRangeInput;
        InputComponentProps: {};
    }, {
        readonly value: "is";
        readonly getApplyFilterFn: (filterItem: GridFilterItem) => ((value: Date | null | undefined, row: GridValidRowModel, column: GridColDef<GridValidRowModel, Date | null | undefined, unknown>, apiRef: React.MutableRefObject<GridApiCommunity>) => boolean) | null;
        readonly InputComponent: typeof GridFilterDateInput;
        readonly InputComponentProps: {
            showTime: boolean;
            filterMode: "server" | "client";
        };
    }, {
        readonly value: "not";
        readonly getApplyFilterFn: (filterItem: GridFilterItem) => ((value: Date | null | undefined, row: GridValidRowModel, column: GridColDef<GridValidRowModel, Date | null | undefined, unknown>, apiRef: React.MutableRefObject<GridApiCommunity>) => boolean) | null;
        readonly InputComponent: typeof GridFilterDateInput;
        readonly InputComponentProps: {
            showTime: boolean;
            filterMode: "server" | "client";
        };
    }, {
        readonly value: "after";
        readonly getApplyFilterFn: (filterItem: GridFilterItem) => ((value: Date | null | undefined, row: GridValidRowModel, column: GridColDef<GridValidRowModel, Date | null | undefined, unknown>, apiRef: React.MutableRefObject<GridApiCommunity>) => boolean) | null;
        readonly InputComponent: typeof GridFilterDateInput;
        readonly InputComponentProps: {
            showTime: boolean;
            filterMode: "server" | "client";
        };
    }, {
        readonly value: "onOrAfter";
        readonly getApplyFilterFn: (filterItem: GridFilterItem) => ((value: Date | null | undefined, row: GridValidRowModel, column: GridColDef<GridValidRowModel, Date | null | undefined, unknown>, apiRef: React.MutableRefObject<GridApiCommunity>) => boolean) | null;
        readonly InputComponent: typeof GridFilterDateInput;
        readonly InputComponentProps: {
            showTime: boolean;
            filterMode: "server" | "client";
        };
    }, {
        readonly value: "before";
        readonly getApplyFilterFn: (filterItem: GridFilterItem) => ((value: Date | null | undefined, row: GridValidRowModel, column: GridColDef<GridValidRowModel, Date | null | undefined, unknown>, apiRef: React.MutableRefObject<GridApiCommunity>) => boolean) | null;
        readonly InputComponent: typeof GridFilterDateInput;
        readonly InputComponentProps: {
            showTime: boolean;
            filterMode: "server" | "client";
        };
    }, {
        readonly value: "onOrBefore";
        readonly getApplyFilterFn: (filterItem: GridFilterItem) => ((value: Date | null | undefined, row: GridValidRowModel, column: GridColDef<GridValidRowModel, Date | null | undefined, unknown>, apiRef: React.MutableRefObject<GridApiCommunity>) => boolean) | null;
        readonly InputComponent: typeof GridFilterDateInput;
        readonly InputComponentProps: {
            showTime: boolean;
            filterMode: "server" | "client";
        };
    }, {
        readonly value: "isEmpty";
        readonly getApplyFilterFn: () => (value: Date | null | undefined) => boolean;
        readonly requiresFilterValue: false;
    }, {
        readonly value: "isNotEmpty";
        readonly getApplyFilterFn: () => (value: Date | null | undefined) => boolean;
        readonly requiresFilterValue: false;
    }];
};
/**
 * Minimum width to make sure that __all__ default formats for different locales are not overflown by default
 */
declare const DATAGRID_DATETIME_COLUMN_WIDTH = 175;
export { useDataGridDateColumns, GridFilterDateRangeInput, DATAGRID_DATETIME_COLUMN_WIDTH, };
export type { GridDateColumnFilterOperator, GridFilterDateRangeInputProps, DataGridDateColDef, DataGridDateTimeColDef, };
